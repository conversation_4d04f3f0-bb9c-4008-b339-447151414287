package examples;

import com.workplat.license.manager.LicenseManager;
import com.workplat.license.model.LicenseInfo;
import com.workplat.license.validator.ValidationResult;

/**
 * License集成示例
 * 展示如何在应用程序中集成License验证功能
 *
 * <AUTHOR> Management Tool
 * @date 2025/08/09
 */
public class LicenseIntegrationExample {

    public static void main(String[] args) {
        System.out.println("=== License集成示例 ===\n");

        // 示例1：基本的License验证
        basicLicenseValidationExample();

        // 示例2：功能权限检查
        featurePermissionExample();

        // 示例3：用户数限制检查
        userLimitExample();

        // 示例4：License管理器使用
        licenseManagerExample();

        // 示例5：应用程序启动时的License检查
        applicationStartupExample();
    }

    /**
     * 示例1：基本的License验证
     */
    public static void basicLicenseValidationExample() {
        System.out.println("=== 基本License验证示例 ===");

        try {
            // 假设这些是您的License文件和公钥
            String licenseFilePath = "./license.lic";
            String publicKey = "您的公钥内容"; // 实际使用时从配置文件或资源文件读取

            // 获取License管理器实例
            LicenseManager licenseManager = LicenseManager.getInstance();

            // 初始化License管理器
            ValidationResult result = licenseManager.initialize(licenseFilePath, publicKey);

            if (result.isSuccess()) {
                System.out.println("✅ License验证成功");
                LicenseInfo license = result.getLicenseInfo();
                System.out.println("产品名称: " + license.getProductName());
                System.out.println("License类型: " + license.getLicenseType().getDescription());
                System.out.println("过期时间: " + license.getExpireDate());
            } else {
                System.out.println("❌ License验证失败: " + result.getMessage());
                // 在实际应用中，这里应该阻止应用程序启动或限制功能
                handleLicenseValidationFailure(result);
            }

        } catch (Exception e) {
            System.err.println("License验证异常: " + e.getMessage());
        }
        System.out.println();
    }

    /**
     * 示例2：功能权限检查
     */
    public static void featurePermissionExample() {
        System.out.println("=== 功能权限检查示例 ===");

        LicenseManager licenseManager = LicenseManager.getInstance();

        // 检查各种功能权限
        String[] features = {
                "BASIC_FEATURES",
                "ADVANCED_FEATURES", 
                "REPORT_FEATURES",
                "EXPORT_FEATURES",
                "ADMIN_FEATURES"
        };

        for (String feature : features) {
            boolean hasPermission = licenseManager.hasFeature(feature);
            System.out.println(String.format("功能 [%s]: %s", 
                    feature, 
                    hasPermission ? "✅ 已授权" : "❌ 未授权"));
        }
        System.out.println();
    }

    /**
     * 示例3：用户数限制检查
     */
    public static void userLimitExample() {
        System.out.println("=== 用户数限制检查示例 ===");

        LicenseManager licenseManager = LicenseManager.getInstance();

        // 模拟不同的用户数场景
        int[] userCounts = {10, 50, 100, 200};

        for (int userCount : userCounts) {
            boolean withinLimit = licenseManager.checkUserLimit(userCount);
            System.out.println(String.format("用户数 [%d]: %s", 
                    userCount, 
                    withinLimit ? "✅ 允许" : "❌ 超限"));
        }
        System.out.println();
    }

    /**
     * 示例4：License管理器使用
     */
    public static void licenseManagerExample() {
        System.out.println("=== License管理器使用示例 ===");

        LicenseManager licenseManager = LicenseManager.getInstance();

        // 获取当前License信息
        LicenseInfo currentLicense = licenseManager.getCurrentLicense();
        if (currentLicense != null) {
            System.out.println("当前License信息:");
            System.out.println("  License ID: " + currentLicense.getLicenseId());
            System.out.println("  客户名称: " + 
                    (currentLicense.getCustomerInfo() != null ? 
                            currentLicense.getCustomerInfo().getCustomerName() : "未知"));
            System.out.println("  License类型: " + currentLicense.getLicenseType().getDescription());
            System.out.println("  最大用户数: " + 
                    (currentLicense.getMaxUsers() == -1 ? "无限制" : currentLicense.getMaxUsers()));
            System.out.println("  是否有效: " + (licenseManager.isLicenseValid() ? "是" : "否"));
        }

        // 重新加载License
        System.out.println("\n重新加载License...");
        ValidationResult reloadResult = licenseManager.reloadLicense();
        System.out.println("重新加载结果: " + 
                (reloadResult.isSuccess() ? "成功" : "失败 - " + reloadResult.getMessage()));

        System.out.println();
    }

    /**
     * 示例5：应用程序启动时的License检查
     */
    public static void applicationStartupExample() {
        System.out.println("=== 应用程序启动License检查示例 ===");

        try {
            // 这是一个典型的应用程序启动时License检查流程
            System.out.println("正在启动应用程序...");

            // 1. 初始化License管理器
            LicenseManager licenseManager = LicenseManager.getInstance();
            
            // 2. 从配置文件读取License路径和公钥
            String licenseFilePath = getLicenseFilePath();
            String publicKey = getPublicKey();

            // 3. 验证License
            ValidationResult result = licenseManager.initialize(licenseFilePath, publicKey);

            if (result.isSuccess()) {
                System.out.println("✅ License验证通过，应用程序启动成功");
                
                // 4. 设置定时验证（可选）
                licenseManager.setValidationInterval(30); // 30分钟检查一次
                licenseManager.setPeriodicValidationEnabled(true);
                
                // 5. 应用程序正常运行
                simulateApplicationRunning(licenseManager);
                
            } else {
                System.out.println("❌ License验证失败，应用程序启动被阻止");
                System.out.println("失败原因: " + result.getMessage());
                
                // 在实际应用中，这里应该退出应用程序
                System.exit(1);
            }

        } catch (Exception e) {
            System.err.println("应用程序启动失败: " + e.getMessage());
            System.exit(1);
        }
        System.out.println();
    }

    /**
     * 模拟应用程序运行
     */
    private static void simulateApplicationRunning(LicenseManager licenseManager) {
        System.out.println("\n应用程序正在运行...");

        // 模拟业务操作中的权限检查
        if (licenseManager.hasFeature("REPORT_FEATURES")) {
            System.out.println("  📊 生成报表功能可用");
        } else {
            System.out.println("  ❌ 生成报表功能不可用");
        }

        if (licenseManager.hasFeature("EXPORT_FEATURES")) {
            System.out.println("  📤 数据导出功能可用");
        } else {
            System.out.println("  ❌ 数据导出功能不可用");
        }

        // 模拟用户登录时的用户数检查
        int currentUsers = 25; // 假设当前有25个用户
        if (licenseManager.checkUserLimit(currentUsers + 1)) {
            System.out.println("  👤 允许新用户登录 (当前用户数: " + currentUsers + ")");
        } else {
            System.out.println("  ❌ 用户数已达上限，拒绝新用户登录");
        }
    }

    /**
     * 处理License验证失败
     */
    private static void handleLicenseValidationFailure(ValidationResult result) {
        System.out.println("处理License验证失败...");
        
        // 根据不同的失败原因采取不同的处理策略
        String message = result.getMessage();
        
        if (message.contains("过期")) {
            System.out.println("  建议：请联系供应商续费License");
        } else if (message.contains("硬件绑定")) {
            System.out.println("  建议：请在授权的机器上运行，或联系供应商重新绑定");
        } else if (message.contains("签名")) {
            System.out.println("  建议：License文件可能被篡改，请重新获取正确的License文件");
        } else {
            System.out.println("  建议：请检查License文件是否正确，或联系技术支持");
        }
    }

    /**
     * 获取License文件路径
     * 实际应用中应该从配置文件读取
     */
    private static String getLicenseFilePath() {
        // 这里可以从配置文件、环境变量或命令行参数读取
        return System.getProperty("license.file.path", "./license.lic");
    }

    /**
     * 获取公钥
     * 实际应用中应该从资源文件或配置文件读取
     */
    private static String getPublicKey() {
        // 这里可以从资源文件读取公钥
        // 为了安全，公钥通常嵌入在应用程序中
        return System.getProperty("license.public.key", "您的公钥内容");
    }
}
