package examples;

import com.workplat.license.crypto.RSAKeyPairGenerator;
import com.workplat.license.generator.LicenseBuilder;
import com.workplat.license.generator.LicenseGenerator;
import com.workplat.license.model.LicenseInfo;
import com.workplat.license.validator.LicenseValidator;
import com.workplat.license.validator.ValidationResult;

/**
 * License验证示例
 * 演示完整的License生成和验证流程
 *
 * <AUTHOR> Management Tool
 * @date 2025/08/09
 */
public class LicenseValidationExample {

    public static void main(String[] args) {
        System.out.println("=== License验证完整示例 ===\n");

        try {
            // 步骤1：生成密钥对
            System.out.println("1. 生成RSA密钥对...");
            RSAKeyPairGenerator.RSAKeyPair keyPair = RSAKeyPairGenerator.generateKeyPair();
            String privateKey = keyPair.getPrivateKey();
            String publicKey = keyPair.getPublicKey();
            System.out.println("✅ 密钥对生成成功");
            System.out.println();

            // 步骤2：生成License文件
            System.out.println("2. 生成License文件...");
            LicenseInfo licenseInfo = createSampleLicense();
            String licenseFilePath = LicenseGenerator.generateLicenseFile(
                    licenseInfo, 
                    privateKey, 
                    "./sample_license.lic"
            );
            System.out.println("✅ License文件生成成功: " + licenseFilePath);
            System.out.println();

            // 步骤3：验证License文件
            System.out.println("3. 验证License文件...");
            ValidationResult result = LicenseValidator.validateLicenseFile(licenseFilePath, publicKey);
            
            if (result.isSuccess()) {
                System.out.println("✅ License验证成功！");
                displayLicenseInfo(result.getLicenseInfo());
            } else {
                System.out.println("❌ License验证失败: " + result.getMessage());
            }
            System.out.println();

            // 步骤4：功能权限检查示例
            System.out.println("4. 功能权限检查示例...");
            if (result.isSuccess()) {
                demonstrateFeatureChecks(result.getLicenseInfo());
            }
            System.out.println();

            // 步骤5：用户数限制检查示例
            System.out.println("5. 用户数限制检查示例...");
            if (result.isSuccess()) {
                demonstrateUserLimitChecks(result.getLicenseInfo());
            }
            System.out.println();

            // 步骤6：错误场景演示
            System.out.println("6. 错误场景演示...");
            demonstrateErrorScenarios(licenseFilePath, publicKey);

        } catch (Exception e) {
            System.err.println("示例执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建示例License
     */
    private static LicenseInfo createSampleLicense() {
        return LicenseBuilder.create()
                .product("AI中央平台", "1.0")
                .customer("示例客户", "DEMO001")
                .type(LicenseInfo.LicenseType.STANDARD)
                .validForDays(365) // 一年有效期
                .bindCurrentMachine() // 绑定当前机器
                .features("BASIC_FEATURES", "REPORT_FEATURES", "EXPORT_FEATURES")
                .maxUsers(50) // 最大50个用户
                .extraData("这是一个演示License")
                .build();
    }

    /**
     * 显示License信息
     */
    private static void displayLicenseInfo(LicenseInfo licenseInfo) {
        System.out.println("License详细信息:");
        System.out.println("  License ID: " + licenseInfo.getLicenseId());
        System.out.println("  产品名称: " + licenseInfo.getProductName());
        System.out.println("  License类型: " + licenseInfo.getLicenseType().getDescription());
        System.out.println("  客户名称: " + licenseInfo.getCustomerInfo().getCustomerName());
        System.out.println("  过期时间: " + licenseInfo.getExpireDate());
        System.out.println("  最大用户数: " + licenseInfo.getMaxUsers());
        System.out.println("  硬件绑定: " + (licenseInfo.getMachineCode() != null ? "是" : "否"));
        
        if (licenseInfo.getAuthorizedFeatures() != null) {
            System.out.println("  授权功能: " + String.join(", ", licenseInfo.getAuthorizedFeatures()));
        }
    }

    /**
     * 演示功能权限检查
     */
    private static void demonstrateFeatureChecks(LicenseInfo licenseInfo) {
        String[] testFeatures = {
                "BASIC_FEATURES",
                "REPORT_FEATURES", 
                "EXPORT_FEATURES",
                "ADMIN_FEATURES",
                "ENTERPRISE_FEATURES"
        };

        System.out.println("功能权限检查结果:");
        for (String feature : testFeatures) {
            boolean hasPermission = LicenseValidator.hasFeature(licenseInfo, feature);
            System.out.println(String.format("  %s: %s", 
                    feature, 
                    hasPermission ? "✅ 已授权" : "❌ 未授权"));
        }
    }

    /**
     * 演示用户数限制检查
     */
    private static void demonstrateUserLimitChecks(LicenseInfo licenseInfo) {
        int[] testUserCounts = {10, 30, 50, 60, 100};

        System.out.println("用户数限制检查结果:");
        for (int userCount : testUserCounts) {
            boolean withinLimit = LicenseValidator.checkUserLimit(licenseInfo, userCount);
            System.out.println(String.format("  用户数 %d: %s", 
                    userCount, 
                    withinLimit ? "✅ 允许" : "❌ 超限"));
        }
    }

    /**
     * 演示错误场景
     */
    private static void demonstrateErrorScenarios(String licenseFilePath, String publicKey) {
        System.out.println("错误场景演示:");

        // 场景1：使用错误的公钥
        System.out.println("  场景1: 使用错误的公钥验证");
        try {
            RSAKeyPairGenerator.RSAKeyPair wrongKeyPair = RSAKeyPairGenerator.generateKeyPair();
            ValidationResult result = LicenseValidator.validateLicenseFile(
                    licenseFilePath, 
                    wrongKeyPair.getPublicKey()
            );
            System.out.println("    结果: " + (result.isSuccess() ? "成功" : "失败 - " + result.getMessage()));
        } catch (Exception e) {
            System.out.println("    结果: 异常 - " + e.getMessage());
        }

        // 场景2：验证不存在的文件
        System.out.println("  场景2: 验证不存在的License文件");
        try {
            ValidationResult result = LicenseValidator.validateLicenseFile(
                    "./nonexistent_license.lic", 
                    publicKey
            );
            System.out.println("    结果: " + (result.isSuccess() ? "成功" : "失败 - " + result.getMessage()));
        } catch (Exception e) {
            System.out.println("    结果: 异常 - " + e.getMessage());
        }

        // 场景3：使用空的公钥
        System.out.println("  场景3: 使用空的公钥");
        try {
            ValidationResult result = LicenseValidator.validateLicenseFile(licenseFilePath, "");
            System.out.println("    结果: " + (result.isSuccess() ? "成功" : "失败 - " + result.getMessage()));
        } catch (Exception e) {
            System.out.println("    结果: 异常 - " + e.getMessage());
        }
    }
}
