package examples;

import com.workplat.license.crypto.RSAKeyPairGenerator;
import com.workplat.license.generator.LicenseBuilder;
import com.workplat.license.generator.LicenseGenerator;
import com.workplat.license.model.LicenseInfo;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 批量License生成器示例
 * 演示如何批量生成不同类型的License文件
 *
 * <AUTHOR> Management Tool
 * @date 2025/08/09
 */
public class BatchLicenseGenerator {

    public static void main(String[] args) {
        System.out.println("=== 批量License生成示例 ===\n");

        try {
            // 1. 生成密钥对
            System.out.println("1. 生成RSA密钥对...");
            RSAKeyPairGenerator.RSAKeyPair keyPair = RSAKeyPairGenerator.generateKeyPair();
            String privateKey = keyPair.getPrivateKey();
            String publicKey = keyPair.getPublicKey();
            System.out.println("✅ 密钥对生成成功");
            System.out.println();

            // 2. 创建输出目录
            String outputDir = "./batch_licenses";
            File dir = new File(outputDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 3. 批量生成不同类型的License
            generateTrialLicenses(privateKey, outputDir);
            generateStandardLicenses(privateKey, outputDir);
            generateEnterpriseLicenses(privateKey, outputDir);

            System.out.println("✅ 批量License生成完成！");
            System.out.println("输出目录: " + new File(outputDir).getAbsolutePath());
            System.out.println();

            // 4. 保存公钥文件
            savePublicKey(publicKey, outputDir);

        } catch (Exception e) {
            System.err.println("批量生成License失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 生成试用版License
     */
    private static void generateTrialLicenses(String privateKey, String outputDir) {
        System.out.println("2. 生成试用版License...");

        String[][] trialCustomers = {
                {"试用客户A", "TRIAL_A", "30"},
                {"试用客户B", "TRIAL_B", "15"},
                {"试用客户C", "TRIAL_C", "7"}
        };

        for (String[] customer : trialCustomers) {
            try {
                String customerName = customer[0];
                String customerCode = customer[1];
                int trialDays = Integer.parseInt(customer[2]);

                LicenseInfo licenseInfo = LicenseBuilder.createTrialLicense(
                        "AI中央平台", 
                        customerName, 
                        trialDays
                ).customer(customerName, customerCode)
                 .build();

                String fileName = String.format("%s/%s_trial_%ddays.lic", 
                        outputDir, customerCode, trialDays);
                
                LicenseGenerator.generateLicenseFile(licenseInfo, privateKey, fileName);
                System.out.println("  ✅ " + customerName + " - " + fileName);

            } catch (Exception e) {
                System.err.println("  ❌ 生成试用License失败: " + customer[0] + " - " + e.getMessage());
            }
        }
        System.out.println();
    }

    /**
     * 生成标准版License
     */
    private static void generateStandardLicenses(String privateKey, String outputDir) {
        System.out.println("3. 生成标准版License...");

        String[][] standardCustomers = {
                {"北京科技有限公司", "BJ001", "365", "50"},
                {"上海创新企业", "SH002", "180", "30"},
                {"深圳技术公司", "SZ003", "365", "100"}
        };

        for (String[] customer : standardCustomers) {
            try {
                String customerName = customer[0];
                String customerCode = customer[1];
                int validDays = Integer.parseInt(customer[2]);
                int maxUsers = Integer.parseInt(customer[3]);

                LicenseInfo licenseInfo = LicenseBuilder.create()
                        .product("AI中央平台", "1.0")
                        .customer(customerName, customerCode)
                        .type(LicenseInfo.LicenseType.STANDARD)
                        .validForDays(validDays)
                        .features("BASIC_FEATURES", "REPORT_FEATURES", "EXPORT_FEATURES")
                        .maxUsers(maxUsers)
                        .extraData("标准版License - " + customerName)
                        .build();

                String fileName = String.format("%s/%s_standard_%ddays.lic", 
                        outputDir, customerCode, validDays);
                
                LicenseGenerator.generateLicenseFile(licenseInfo, privateKey, fileName);
                System.out.println("  ✅ " + customerName + " - " + fileName);

            } catch (Exception e) {
                System.err.println("  ❌ 生成标准License失败: " + customer[0] + " - " + e.getMessage());
            }
        }
        System.out.println();
    }

    /**
     * 生成企业版License
     */
    private static void generateEnterpriseLicenses(String privateKey, String outputDir) {
        System.out.println("4. 生成企业版License...");

        String[][] enterpriseCustomers = {
                {"大型企业集团A", "ENT001", "730", "-1"}, // 2年，无用户限制
                {"跨国公司B", "ENT002", "365", "500"},    // 1年，500用户
                {"政府机构C", "GOV003", "1095", "200"}   // 3年，200用户
        };

        for (String[] customer : enterpriseCustomers) {
            try {
                String customerName = customer[0];
                String customerCode = customer[1];
                int validDays = Integer.parseInt(customer[2]);
                int maxUsers = Integer.parseInt(customer[3]);

                LicenseInfo licenseInfo = LicenseBuilder.create()
                        .product("AI中央平台", "1.0")
                        .customer(customerName, customerCode)
                        .type(LicenseInfo.LicenseType.ENTERPRISE)
                        .validForDays(validDays)
                        .features("ALL_FEATURES", "ADMIN_FEATURES", "ENTERPRISE_FEATURES", 
                                "ADVANCED_ANALYTICS", "CUSTOM_INTEGRATION")
                        .maxUsers(maxUsers)
                        .extraData("企业版License - " + customerName + " - 完整功能授权")
                        .build();

                String fileName = String.format("%s/%s_enterprise_%ddays.lic", 
                        outputDir, customerCode, validDays);
                
                LicenseGenerator.generateLicenseFile(licenseInfo, privateKey, fileName);
                System.out.println("  ✅ " + customerName + " - " + fileName);

            } catch (Exception e) {
                System.err.println("  ❌ 生成企业License失败: " + customer[0] + " - " + e.getMessage());
            }
        }
        System.out.println();
    }

    /**
     * 保存公钥文件
     */
    private static void savePublicKey(String publicKey, String outputDir) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String publicKeyFileName = outputDir + "/public_key_" + timestamp + ".txt";
            
            String publicKeyContent = "# AI中央平台 - License验证公钥\n" +
                    "# 生成时间: " + LocalDateTime.now() + "\n" +
                    "# 用途: 用于验证License文件的数字签名\n" +
                    "# 注意: 请妥善保管此公钥，集成到应用程序中使用\n" +
                    "\n" +
                    publicKey;
            
            java.nio.file.Files.write(
                    java.nio.file.Paths.get(publicKeyFileName), 
                    publicKeyContent.getBytes("UTF-8")
            );
            
            System.out.println("5. 公钥文件已保存: " + publicKeyFileName);
            
        } catch (Exception e) {
            System.err.println("保存公钥文件失败: " + e.getMessage());
        }
    }
}
