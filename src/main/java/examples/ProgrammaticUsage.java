package examples;

import com.workplat.license.crypto.RSAKeyPairGenerator;
import com.workplat.license.generator.LicenseBuilder;
import com.workplat.license.generator.LicenseGenerator;
import com.workplat.license.model.LicenseInfo;
import com.workplat.license.util.MachineCodeUtil;

import java.io.File;
import java.time.LocalDateTime;

/**
 * License管理工具编程使用示例
 * 演示如何在代码中使用License管理功能
 */
public class ProgrammaticUsage {

    public static void main(String[] args) {
        try {
            // 示例1：生成密钥对
            generateKeyPairExample();
            
            // 示例2：生成标准License
            generateStandardLicenseExample();
            
            // 示例3：生成试用License
            generateTrialLicenseExample();
            
            // 示例4：生成企业License
            generateEnterpriseLicenseExample();
            
            // 示例5：批量生成License
            batchGenerateLicenseExample();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 示例1：生成密钥对
     */
    public static void generateKeyPairExample() {
        System.out.println("=== 生成密钥对示例 ===");
        
        // 生成2048位RSA密钥对
        RSAKeyPairGenerator.RSAKeyPair keyPair = RSAKeyPairGenerator.generateKeyPair(2048);
        
        System.out.println("公钥: " + keyPair.getPublicKey());
        System.out.println("私钥: " + keyPair.getPrivateKey());
        System.out.println("密钥长度: " + keyPair.getKeySize() + " bits");
        System.out.println();
    }

    /**
     * 示例2：生成标准License
     */
    public static void generateStandardLicenseExample() {
        System.out.println("=== 生成标准License示例 ===");
        
        try {
            // 1. 生成密钥对（实际使用中应该使用已有的密钥）
            RSAKeyPairGenerator.RSAKeyPair keyPair = RSAKeyPairGenerator.generateKeyPair();
            String privateKey = keyPair.getPrivateKey();
            
            // 2. 构建License信息
            LicenseInfo licenseInfo = LicenseBuilder.create()
                    .product("AI中央平台", "1.0")
                    .customer("北京科技有限公司", "*********")
                    .type(LicenseInfo.LicenseType.STANDARD)
                    .validForDays(365)  // 一年有效期
                    .bindCurrentMachine()  // 绑定当前机器
                    .features("BASIC_FEATURES", "REPORT_FEATURES", "EXPORT_FEATURES")
                    .maxUsers(50)  // 最大50个用户
                    .extraData("Generated by API example")
                    .build();
            
            // 3. 生成License文件
            String licenseFilePath = LicenseGenerator.generateLicenseFile(
                    licenseInfo, 
                    privateKey, 
                    "./standard_license.lic"
            );
            
            System.out.println("标准License生成成功: " + licenseFilePath);
            
        } catch (Exception e) {
            System.err.println("生成标准License失败: " + e.getMessage());
        }
        System.out.println();
    }

    /**
     * 示例3：生成试用License
     */
    public static void generateTrialLicenseExample() {
        System.out.println("=== 生成试用License示例 ===");
        
        try {
            // 使用便捷方法生成试用License
            RSAKeyPairGenerator.RSAKeyPair keyPair = RSAKeyPairGenerator.generateKeyPair();
            
            LicenseInfo trialLicense = LicenseBuilder.createTrialLicense(
                    "AI中央平台", 
                    "试用客户", 
                    30  // 30天试用期
            ).build();
            
            String licenseFilePath = LicenseGenerator.generateLicenseFile(
                    trialLicense, 
                    keyPair.getPrivateKey(), 
                    "./trial_license.lic"
            );
            
            System.out.println("试用License生成成功: " + licenseFilePath);
            
        } catch (Exception e) {
            System.err.println("生成试用License失败: " + e.getMessage());
        }
        System.out.println();
    }

    /**
     * 示例4：生成企业License
     */
    public static void generateEnterpriseLicenseExample() {
        System.out.println("=== 生成企业License示例 ===");
        
        try {
            RSAKeyPairGenerator.RSAKeyPair keyPair = RSAKeyPairGenerator.generateKeyPair();
            
            // 获取指定机器的机器码（实际使用中应该从客户那里获取）
            String targetMachineCode = "A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6";
            
            LicenseInfo enterpriseLicense = LicenseBuilder.create()
                    .product("AI中央平台", "2.0")
                    .customer("大型企业集团", "ENT001", "张经理", "13800138000", "<EMAIL>", "北京市朝阳区")
                    .type(LicenseInfo.LicenseType.ENTERPRISE)
                    .effectiveDate(LocalDateTime.now())
                    .expireDate(LocalDateTime.now().plusYears(2))  // 两年有效期
                    .bindMachine(targetMachineCode)  // 绑定指定机器
                    .features("ALL_FEATURES", "ADMIN_FEATURES", "ENTERPRISE_FEATURES", "API_ACCESS")
                    .maxUsers(-1)  // 无用户数限制
                    .extraData("Enterprise license with full features")
                    .build();
            
            String licenseFilePath = LicenseGenerator.generateLicenseFile(
                    enterpriseLicense, 
                    keyPair.getPrivateKey(), 
                    new File("./licenses")  // 输出到licenses目录
            );
            
            System.out.println("企业License生成成功: " + licenseFilePath);
            
        } catch (Exception e) {
            System.err.println("生成企业License失败: " + e.getMessage());
        }
        System.out.println();
    }

    /**
     * 示例5：批量生成License
     */
    public static void batchGenerateLicenseExample() {
        System.out.println("=== 批量生成License示例 ===");
        
        try {
            // 生成一次密钥对，用于所有License
            RSAKeyPairGenerator.RSAKeyPair keyPair = RSAKeyPairGenerator.generateKeyPair();
            String privateKey = keyPair.getPrivateKey();
            
            // 客户信息列表
            String[][] customers = {
                {"客户A", "CUST001", "365", "STANDARD"},
                {"客户B", "CUST002", "180", "TRIAL"},
                {"客户C", "CUST003", "730", "ENTERPRISE"}
            };
            
            File outputDir = new File("./batch_licenses");
            if (!outputDir.exists()) {
                outputDir.mkdirs();
            }
            
            for (String[] customer : customers) {
                String customerName = customer[0];
                String customerCode = customer[1];
                int validDays = Integer.parseInt(customer[2]);
                String licenseTypeStr = customer[3];
                
                LicenseInfo.LicenseType licenseType = LicenseInfo.LicenseType.valueOf(licenseTypeStr);
                
                LicenseInfo licenseInfo = LicenseBuilder.create()
                        .product("AI中央平台", "1.0")
                        .customer(customerName, customerCode)
                        .type(licenseType)
                        .validForDays(validDays)
                        .features(getFeaturesByType(licenseType))
                        .maxUsers(getUserLimitByType(licenseType))
                        .build();
                
                String licenseFilePath = LicenseGenerator.generateLicenseFile(
                        licenseInfo, 
                        privateKey, 
                        outputDir
                );
                
                System.out.println("为 " + customerName + " 生成License: " + licenseFilePath);
            }
            
        } catch (Exception e) {
            System.err.println("批量生成License失败: " + e.getMessage());
        }
        System.out.println();
    }

    /**
     * 根据License类型获取功能列表
     */
    private static String[] getFeaturesByType(LicenseInfo.LicenseType licenseType) {
        switch (licenseType) {
            case TRIAL:
                return new String[]{"BASIC_FEATURES"};
            case STANDARD:
                return new String[]{"BASIC_FEATURES", "REPORT_FEATURES"};
            case ENTERPRISE:
                return new String[]{"ALL_FEATURES", "ADMIN_FEATURES", "ENTERPRISE_FEATURES"};
            case DEVELOPMENT:
                return new String[]{"ALL_FEATURES", "DEBUG_FEATURES"};
            default:
                return new String[]{"BASIC_FEATURES"};
        }
    }

    /**
     * 根据License类型获取用户数限制
     */
    private static int getUserLimitByType(LicenseInfo.LicenseType licenseType) {
        switch (licenseType) {
            case TRIAL:
                return 5;
            case STANDARD:
                return 50;
            case ENTERPRISE:
                return -1;  // 无限制
            case DEVELOPMENT:
                return 10;
            default:
                return 10;
        }
    }

    /**
     * 示例：读取和验证License文件
     */
    public static void readLicenseExample(String licenseFilePath) {
        System.out.println("=== 读取License示例 ===");
        
        try {
            // 读取License文件内容
            String licenseContent = LicenseGenerator.readLicenseFileContent(licenseFilePath);
            
            // 解析License信息
            LicenseInfo licenseInfo = LicenseGenerator.parseLicenseFromContent(licenseContent);
            
            // 显示License信息
            System.out.println("License ID: " + licenseInfo.getLicenseId());
            System.out.println("产品名称: " + licenseInfo.getProductName());
            System.out.println("客户名称: " + licenseInfo.getCustomerInfo().getCustomerName());
            System.out.println("License类型: " + licenseInfo.getLicenseType().getDescription());
            System.out.println("过期时间: " + licenseInfo.getExpireDate());
            System.out.println("剩余天数: " + licenseInfo.getRemainingDays());
            System.out.println("是否过期: " + licenseInfo.isExpired());
            System.out.println("授权功能: " + licenseInfo.getAuthorizedFeatures());
            
        } catch (Exception e) {
            System.err.println("读取License失败: " + e.getMessage());
        }
        System.out.println();
    }

    /**
     * 示例：获取机器码
     */
    public static void getMachineCodeExample() {
        System.out.println("=== 获取机器码示例 ===");
        
        String machineCode = MachineCodeUtil.getMachineCode();
        System.out.println("当前机器码: " + machineCode);
        
        // 验证机器码
        boolean isValid = MachineCodeUtil.validateMachineCode(machineCode);
        System.out.println("机器码验证结果: " + isValid);
        System.out.println();
    }
}
