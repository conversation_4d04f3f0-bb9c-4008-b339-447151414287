package com.workplat.license.manager;

import com.workplat.license.model.LicenseInfo;
import com.workplat.license.validator.LicenseValidator;
import com.workplat.license.validator.ValidationResult;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * License管理器
 * 提供License加载、验证、缓存和权限检查功能
 *
 * <AUTHOR> Management Tool
 * @date 2025/08/09
 */
@Slf4j
public class LicenseManager {

    /**
     * 单例实例
     */
    private static volatile LicenseManager instance;

    /**
     * 当前加载的License信息
     */
    private volatile LicenseInfo currentLicense;

    /**
     * 公钥（用于验证）
     */
    private String publicKey;

    /**
     * License文件路径
     */
    private String licenseFilePath;

    /**
     * 验证结果缓存
     */
    private final ConcurrentHashMap<String, ValidationResult> validationCache = new ConcurrentHashMap<>();

    /**
     * 定时验证任务
     */
    private ScheduledExecutorService scheduler;

    /**
     * 验证间隔（分钟）
     */
    private int validationIntervalMinutes = 60;

    /**
     * 是否启用定时验证
     */
    private boolean enablePeriodicValidation = true;

    /**
     * 私有构造函数
     */
    private LicenseManager() {
        // 私有构造函数，防止外部实例化
    }

    /**
     * 获取单例实例
     *
     * @return License管理器实例
     */
    public static LicenseManager getInstance() {
        if (instance == null) {
            synchronized (LicenseManager.class) {
                if (instance == null) {
                    instance = new LicenseManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化License管理器
     *
     * @param licenseFilePath License文件路径
     * @param publicKey 公钥
     * @return 初始化结果
     */
    public ValidationResult initialize(String licenseFilePath, String publicKey) {
        this.licenseFilePath = licenseFilePath;
        this.publicKey = publicKey;

        // 加载并验证License
        ValidationResult result = loadAndValidateLicense();
        
        if (result.isSuccess()) {
            this.currentLicense = result.getLicenseInfo();
            
            // 启动定时验证
            if (enablePeriodicValidation) {
                startPeriodicValidation();
            }
            
            log.info("License管理器初始化成功: {}", currentLicense.getLicenseId());
        } else {
            log.error("License管理器初始化失败: {}", result.getMessage());
        }

        return result;
    }

    /**
     * 加载并验证License
     *
     * @return 验证结果
     */
    public ValidationResult loadAndValidateLicense() {
        if (licenseFilePath == null || publicKey == null) {
            return ValidationResult.failure("License文件路径或公钥未设置");
        }

        try {
            ValidationResult result = LicenseValidator.validateLicenseFile(licenseFilePath, publicKey);
            
            // 缓存验证结果
            String cacheKey = "license_validation_" + System.currentTimeMillis();
            validationCache.put(cacheKey, result);
            
            // 清理旧的缓存（保留最近10个结果）
            if (validationCache.size() > 10) {
                String oldestKey = validationCache.keySet().iterator().next();
                validationCache.remove(oldestKey);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("加载验证License失败", e);
            return ValidationResult.failure("加载验证License时发生错误: " + e.getMessage());
        }
    }

    /**
     * 检查是否有指定功能权限
     *
     * @param feature 功能名称
     * @return true-有权限，false-无权限
     */
    public boolean hasFeature(String feature) {
        if (currentLicense == null) {
            log.warn("License未加载，功能权限检查失败: {}", feature);
            return false;
        }

        boolean hasPermission = LicenseValidator.hasFeature(currentLicense, feature);
        log.debug("功能权限检查 [{}]: {}", feature, hasPermission ? "允许" : "拒绝");
        
        return hasPermission;
    }

    /**
     * 检查用户数限制
     *
     * @param currentUserCount 当前用户数
     * @return true-未超限，false-已超限
     */
    public boolean checkUserLimit(int currentUserCount) {
        if (currentLicense == null) {
            log.warn("License未加载，用户数限制检查失败");
            return false;
        }

        boolean withinLimit = LicenseValidator.checkUserLimit(currentLicense, currentUserCount);
        log.debug("用户数限制检查 [当前:{}/最大:{}]: {}", 
                currentUserCount, 
                currentLicense.getMaxUsers(), 
                withinLimit ? "通过" : "超限");
        
        return withinLimit;
    }

    /**
     * 获取当前License信息
     *
     * @return License信息
     */
    public LicenseInfo getCurrentLicense() {
        return currentLicense;
    }

    /**
     * 检查License是否有效
     *
     * @return true-有效，false-无效
     */
    public boolean isLicenseValid() {
        if (currentLicense == null) {
            return false;
        }

        // 快速检查时间有效性
        return !currentLicense.isExpired() && currentLicense.isEffective();
    }

    /**
     * 重新加载License
     *
     * @return 验证结果
     */
    public ValidationResult reloadLicense() {
        log.info("重新加载License...");
        ValidationResult result = loadAndValidateLicense();
        
        if (result.isSuccess()) {
            this.currentLicense = result.getLicenseInfo();
            log.info("License重新加载成功");
        } else {
            log.error("License重新加载失败: {}", result.getMessage());
        }
        
        return result;
    }

    /**
     * 启动定时验证
     */
    private void startPeriodicValidation() {
        if (scheduler != null) {
            scheduler.shutdown();
        }

        scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "License-Validator");
            t.setDaemon(true);
            return t;
        });

        scheduler.scheduleAtFixedRate(() -> {
            try {
                log.debug("执行定时License验证...");
                ValidationResult result = loadAndValidateLicense();
                
                if (result.isFailure()) {
                    log.warn("定时License验证失败: {}", result.getMessage());
                    // 这里可以添加License失效的处理逻辑
                    handleLicenseInvalid(result);
                } else {
                    this.currentLicense = result.getLicenseInfo();
                    log.debug("定时License验证成功");
                }
                
            } catch (Exception e) {
                log.error("定时License验证异常", e);
            }
        }, validationIntervalMinutes, validationIntervalMinutes, TimeUnit.MINUTES);

        log.info("定时License验证已启动，间隔: {} 分钟", validationIntervalMinutes);
    }

    /**
     * 处理License失效
     */
    private void handleLicenseInvalid(ValidationResult result) {
        log.error("License验证失败: {}", result.getMessage());
        // 这里可以添加License失效时的处理逻辑，例如：
        // 1. 发送通知
        // 2. 限制功能使用
        // 3. 记录审计日志
        // 4. 触发License更新流程
    }

    /**
     * 设置验证间隔
     *
     * @param intervalMinutes 间隔分钟数
     */
    public void setValidationInterval(int intervalMinutes) {
        this.validationIntervalMinutes = intervalMinutes;
        
        if (scheduler != null && enablePeriodicValidation) {
            startPeriodicValidation(); // 重启定时任务
        }
    }

    /**
     * 启用或禁用定时验证
     *
     * @param enable true-启用，false-禁用
     */
    public void setPeriodicValidationEnabled(boolean enable) {
        this.enablePeriodicValidation = enable;
        
        if (enable && scheduler == null) {
            startPeriodicValidation();
        } else if (!enable && scheduler != null) {
            scheduler.shutdown();
            scheduler = null;
        }
    }

    /**
     * 关闭License管理器
     */
    public void shutdown() {
        if (scheduler != null) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        validationCache.clear();
        currentLicense = null;
        
        log.info("License管理器已关闭");
    }

    /**
     * 获取验证历史
     *
     * @return 验证结果缓存
     */
    public ConcurrentHashMap<String, ValidationResult> getValidationHistory() {
        return new ConcurrentHashMap<>(validationCache);
    }
}
