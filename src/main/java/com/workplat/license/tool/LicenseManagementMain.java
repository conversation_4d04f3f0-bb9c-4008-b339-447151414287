package com.workplat.license.tool;

import com.workplat.license.crypto.RSAKeyPairGenerator;
import com.workplat.license.generator.LicenseBuilder;
import com.workplat.license.generator.LicenseGenerator;
import com.workplat.license.model.LicenseInfo;
import com.workplat.license.util.MachineCodeUtil;
import com.workplat.license.validator.LicenseValidator;
import com.workplat.license.validator.ValidationResult;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Scanner;

/**
 * License管理工具主程序
 * 提供License生成、密钥管理等功能
 *
 * <AUTHOR> Management Tool
 * @date 2025/08/08
 */
public class LicenseManagementMain {

    private static final Scanner scanner = new Scanner(System.in);

    public static void main(String[] args) {
        try {
            System.out.println("=== License管理工具 ===");
            System.out.println("版本: 1.0.0");
            System.out.println();

            while (true) {
                showMainMenu();
                int choice = getIntInput("请选择操作");

                switch (choice) {
                    case 1:
                        generateKeyPair();
                        break;
                    case 2:
                        generateLicense();
                        break;
                    case 3:
                        generateTrialLicense();
                        break;
                    case 4:
                        showMachineCode();
                        break;
                    case 5:
                        viewLicenseInfo();
                        break;
                    case 6:
                        validateLicense();
                        break;
                    case 0:
                        System.out.println("退出程序");
                        return;
                    default:
                        System.out.println("无效选择，请重新输入");
                }

                System.out.println();
                System.out.println("按回车键继续...");
                scanner.nextLine();
            }

        } catch (Exception e) {
            System.err.println("程序执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 显示主菜单
     */
    private static void showMainMenu() {
        System.out.println("请选择操作：");
        System.out.println("1. 生成RSA密钥对");
        System.out.println("2. 生成License文件");
        System.out.println("3. 生成试用License");
        System.out.println("4. 查看当前机器码");
        System.out.println("5. 查看License信息");
        System.out.println("6. 验证License文件");
        System.out.println("0. 退出");
        System.out.println();
    }

    /**
     * 生成RSA密钥对
     */
    private static void generateKeyPair() {
        System.out.println("=== 生成RSA密钥对 ===");

        int keySize = getIntInputWithDefault("请输入密钥长度（默认2048）", 2048);

        RSAKeyPairGenerator.RSAKeyPair keyPair = RSAKeyPairGenerator.generateKeyPair(keySize);

        System.out.println("密钥对生成成功！");
        System.out.println();
        keyPair.printKeyPair();

        // 保存到文件
        String saveChoice = getStringInputWithDefault("是否保存到文件？(y/n)", "y");
        if ("y".equalsIgnoreCase(saveChoice)) {
            saveKeyPairToFile(keyPair);
        }
    }

    /**
     * 生成License文件
     */
    private static void generateLicense() {
        System.out.println("=== 生成License文件 ===");

        try {
            // 获取基本信息
            String productName = getStringInputWithDefault("产品名称", "AI中央平台");
            String productVersion = getStringInputWithDefault("产品版本", "1.0");
            String customerName = getStringInput("客户名称");
            String customerCode = getStringInput("客户代码");

            // 获取License类型
            System.out.println("License类型：");
            System.out.println("1. 试用版 (TRIAL)");
            System.out.println("2. 标准版 (STANDARD)");
            System.out.println("3. 企业版 (ENTERPRISE)");
            System.out.println("4. 开发版 (DEVELOPMENT)");
            int typeChoice = getIntInputWithDefault("请选择License类型", 2);
            LicenseInfo.LicenseType licenseType = getLicenseTypeByChoice(typeChoice);

            // 获取有效期
            int validDays = getIntInputWithDefault("有效期（天数）", 365);

            // 获取机器绑定选择
            String bindChoice = getStringInputWithDefault("是否绑定当前机器？(y/n)", "y");
            String machineCode = null;
            if ("y".equalsIgnoreCase(bindChoice)) {
                machineCode = MachineCodeUtil.getMachineCode();
                System.out.println("当前机器码: " + machineCode);
            } else {
                String customMachineChoice = getStringInputWithDefault("是否指定机器码？(y/n)", "n");
                if ("y".equalsIgnoreCase(customMachineChoice)) {
                    machineCode = getStringInput("请输入机器码");
                }
            }

            // 获取功能权限
            String features = getStringInputWithDefault("授权功能（逗号分隔，默认ALL_FEATURES）", "ALL_FEATURES");

            // 获取私钥
            String privateKey = getStringInput("请输入私钥（用于签名）");

            // 构建License
            LicenseBuilder builder = LicenseBuilder.create()
                    .product(productName, productVersion)
                    .customer(customerName, customerCode)
                    .type(licenseType)
                    .validForDays(validDays);

            if (machineCode != null) {
                builder.bindMachine(machineCode);
            }

            if (!features.trim().isEmpty()) {
                builder.features(features.split(","));
            }

            LicenseInfo licenseInfo = builder.build();

            // 生成License文件
            String outputDir = getStringInputWithDefault("输出目录", "./");
            String licenseFilePath = LicenseGenerator.generateLicenseFile(licenseInfo, privateKey, new File(outputDir));

            System.out.println("License文件生成成功: " + licenseFilePath);

        } catch (Exception e) {
            System.err.println("生成License失败: " + e.getMessage());
        }
    }

    /**
     * 生成试用License
     */
    private static void generateTrialLicense() {
        System.out.println("=== 生成试用License ===");

        try {
            String customerName = getStringInput("客户名称");
            int trialDays = getIntInputWithDefault("试用天数", 30);
            String privateKey = getStringInput("请输入私钥（用于签名）");

            LicenseInfo licenseInfo = LicenseBuilder.createTrialLicense("AI中央平台", customerName, trialDays).build();

            String outputDir = getStringInputWithDefault("输出目录", "./");
            String licenseFilePath = LicenseGenerator.generateLicenseFile(licenseInfo, privateKey, new File(outputDir));

            System.out.println("试用License生成成功: " + licenseFilePath);

        } catch (Exception e) {
            System.err.println("生成试用License失败: " + e.getMessage());
        }
    }

    /**
     * 显示当前机器码
     */
    private static void showMachineCode() {
        System.out.println("=== 当前机器码 ===");
        String machineCode = MachineCodeUtil.getMachineCode();
        System.out.println("机器码: " + machineCode);
        System.out.println("生成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("说明: 此机器码用于License硬件绑定");
    }

    /**
     * 查看License信息
     */
    private static void viewLicenseInfo() {
        System.out.println("=== License信息 ===");

        try {
            String licenseFilePath = getStringInput("请输入License文件路径");
            
            String licenseContent = LicenseGenerator.readLicenseFileContent(licenseFilePath);
            LicenseInfo licenseInfo = LicenseGenerator.parseLicenseFromContent(licenseContent);

            System.out.println("License ID: " + licenseInfo.getLicenseId());
            System.out.println("产品名称: " + licenseInfo.getProductName());
            System.out.println("产品版本: " + licenseInfo.getProductVersion());
            
            if (licenseInfo.getCustomerInfo() != null) {
                System.out.println("客户名称: " + licenseInfo.getCustomerInfo().getCustomerName());
                System.out.println("客户代码: " + licenseInfo.getCustomerInfo().getCustomerCode());
            }
            
            System.out.println("License类型: " + (licenseInfo.getLicenseType() != null ? 
                    licenseInfo.getLicenseType().getDescription() : "未知"));
            System.out.println("发行时间: " + licenseInfo.getIssueDate());
            System.out.println("生效时间: " + licenseInfo.getEffectiveDate());
            System.out.println("过期时间: " + licenseInfo.getExpireDate());
            System.out.println("剩余天数: " + licenseInfo.getRemainingDays());
            System.out.println("最大用户数: " + licenseInfo.getMaxUsers());
            System.out.println("授权功能: " + licenseInfo.getAuthorizedFeatures());
            System.out.println("机器码: " + licenseInfo.getMachineCode());
            
            if (licenseInfo.getExtraData() != null) {
                System.out.println("扩展信息: " + licenseInfo.getExtraData());
            }

        } catch (Exception e) {
            System.err.println("查看License信息失败: " + e.getMessage());
        }
    }

    /**
     * 保存密钥对到文件
     */
    private static void saveKeyPairToFile(RSAKeyPairGenerator.RSAKeyPair keyPair) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String publicKeyFile = "public_key_" + timestamp + ".txt";
            String privateKeyFile = "private_key_" + timestamp + ".txt";

            // 保存公钥
            try (java.io.FileWriter writer = new java.io.FileWriter(publicKeyFile)) {
                writer.write(keyPair.getPublicKey());
            }

            // 保存私钥
            try (java.io.FileWriter writer = new java.io.FileWriter(privateKeyFile)) {
                writer.write(keyPair.getPrivateKey());
            }

            System.out.println("密钥对已保存:");
            System.out.println("公钥文件: " + publicKeyFile);
            System.out.println("私钥文件: " + privateKeyFile);

        } catch (Exception e) {
            System.err.println("保存密钥对失败: " + e.getMessage());
        }
    }

    /**
     * 根据选择获取License类型
     */
    private static LicenseInfo.LicenseType getLicenseTypeByChoice(int choice) {
        switch (choice) {
            case 1: return LicenseInfo.LicenseType.TRIAL;
            case 2: return LicenseInfo.LicenseType.STANDARD;
            case 3: return LicenseInfo.LicenseType.ENTERPRISE;
            case 4: return LicenseInfo.LicenseType.DEVELOPMENT;
            default: return LicenseInfo.LicenseType.STANDARD;
        }
    }

    // 工具方法
    private static String getStringInput(String prompt) {
        System.out.print(prompt + ": ");
        return scanner.nextLine().trim();
    }

    private static String getStringInputWithDefault(String prompt, String defaultValue) {
        System.out.print(prompt + " [" + defaultValue + "]: ");
        String input = scanner.nextLine().trim();
        return input.isEmpty() ? defaultValue : input;
    }

    private static int getIntInput(String prompt) {
        while (true) {
            try {
                System.out.print(prompt + ": ");
                return Integer.parseInt(scanner.nextLine().trim());
            } catch (NumberFormatException e) {
                System.out.println("请输入有效的数字");
            }
        }
    }

    private static int getIntInputWithDefault(String prompt, int defaultValue) {
        System.out.print(prompt + " [" + defaultValue + "]: ");
        String input = scanner.nextLine().trim();
        if (input.isEmpty()) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(input);
        } catch (NumberFormatException e) {
            System.out.println("输入无效，使用默认值: " + defaultValue);
            return defaultValue;
        }
    }

    /**
     * 验证License文件
     */
    private static void validateLicense() {
        System.out.println("=== 验证License文件 ===");

        try {
            String licenseFilePath = getStringInput("请输入License文件路径");
            String publicKey = getStringInput("请输入公钥（用于验证签名）");

            System.out.println("正在验证License文件...");
            ValidationResult result = LicenseValidator.validateLicenseFile(licenseFilePath, publicKey);

            if (result.isSuccess()) {
                System.out.println("✅ License验证成功！");
                System.out.println();

                LicenseInfo licenseInfo = result.getLicenseInfo();
                displayLicenseValidationInfo(licenseInfo);

                // 显示功能权限信息
                displayFeaturePermissions(licenseInfo);

            } else {
                System.out.println("❌ License验证失败！");
                System.out.println("失败原因: " + result.getMessage());

                // 提供解决建议
                provideSolutionSuggestions(result.getMessage());
            }

        } catch (Exception e) {
            System.err.println("验证License文件失败: " + e.getMessage());
        }
    }

    /**
     * 显示License验证信息
     */
    private static void displayLicenseValidationInfo(LicenseInfo licenseInfo) {
        System.out.println("License详细信息:");
        System.out.println("  License ID: " + licenseInfo.getLicenseId());
        System.out.println("  产品名称: " + licenseInfo.getProductName());
        System.out.println("  产品版本: " + licenseInfo.getProductVersion());
        System.out.println("  License类型: " + licenseInfo.getLicenseType().getDescription());

        if (licenseInfo.getCustomerInfo() != null) {
            System.out.println("  客户名称: " + licenseInfo.getCustomerInfo().getCustomerName());
            System.out.println("  客户代码: " + licenseInfo.getCustomerInfo().getCustomerCode());
        }

        System.out.println("  发行日期: " + licenseInfo.getIssueDate());
        System.out.println("  生效日期: " + licenseInfo.getEffectiveDate());
        System.out.println("  过期日期: " + licenseInfo.getExpireDate());

        if (licenseInfo.getMachineCode() != null && !licenseInfo.getMachineCode().trim().isEmpty()) {
            System.out.println("  硬件绑定: 是");
            System.out.println("  绑定机器码: " + licenseInfo.getMachineCode());

            String currentMachineCode = MachineCodeUtil.getMachineCode();
            boolean machineMatch = licenseInfo.getMachineCode().equals(currentMachineCode);
            System.out.println("  当前机器码: " + currentMachineCode);
            System.out.println("  机器码匹配: " + (machineMatch ? "✅ 是" : "❌ 否"));
        } else {
            System.out.println("  硬件绑定: 否");
        }

        System.out.println("  最大用户数: " +
                (licenseInfo.getMaxUsers() == -1 ? "无限制" : licenseInfo.getMaxUsers()));

        if (licenseInfo.getExtraData() != null && !licenseInfo.getExtraData().trim().isEmpty()) {
            System.out.println("  扩展信息: " + licenseInfo.getExtraData());
        }

        // 显示时间状态
        System.out.println("  当前状态: " + getLicenseStatus(licenseInfo));
        System.out.println();
    }

    /**
     * 显示功能权限信息
     */
    private static void displayFeaturePermissions(LicenseInfo licenseInfo) {
        System.out.println("授权功能列表:");
        if (licenseInfo.getAuthorizedFeatures() != null && !licenseInfo.getAuthorizedFeatures().isEmpty()) {
            for (String feature : licenseInfo.getAuthorizedFeatures()) {
                System.out.println("  ✅ " + feature);
            }
        } else {
            System.out.println("  无特定功能授权");
        }
        System.out.println();
    }

    /**
     * 获取License状态描述
     */
    private static String getLicenseStatus(LicenseInfo licenseInfo) {
        if (licenseInfo.isExpired()) {
            return "❌ 已过期";
        } else if (!licenseInfo.isEffective()) {
            return "⏳ 尚未生效";
        } else {
            return "✅ 有效";
        }
    }

    /**
     * 提供解决建议
     */
    private static void provideSolutionSuggestions(String errorMessage) {
        System.out.println();
        System.out.println("解决建议:");

        if (errorMessage.contains("过期")) {
            System.out.println("  • License已过期，请联系供应商续费或获取新的License");
        } else if (errorMessage.contains("硬件绑定") || errorMessage.contains("机器码")) {
            System.out.println("  • 硬件绑定验证失败，请确认在正确的机器上运行");
            System.out.println("  • 如需更换机器，请联系供应商重新绑定License");
        } else if (errorMessage.contains("签名")) {
            System.out.println("  • License文件可能被篡改或损坏");
            System.out.println("  • 请重新获取正确的License文件");
            System.out.println("  • 确认使用正确的公钥进行验证");
        } else if (errorMessage.contains("文件")) {
            System.out.println("  • 检查License文件路径是否正确");
            System.out.println("  • 确认License文件格式是否正确");
        } else if (errorMessage.contains("公钥")) {
            System.out.println("  • 检查公钥格式是否正确");
            System.out.println("  • 确认使用与License匹配的公钥");
        } else {
            System.out.println("  • 请检查License文件和公钥是否正确");
            System.out.println("  • 如问题持续，请联系技术支持");
        }
    }
}
