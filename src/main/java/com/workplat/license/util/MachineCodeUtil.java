package com.workplat.license.util;

import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 机器码生成工具类
 * 基于硬件信息生成唯一的机器标识码
 *
 * <AUTHOR> Management Tool
 * @date 2025/08/08
 */
@Slf4j
public class MachineCodeUtil {

    /**
     * 获取机器码
     * 基于CPU序列号、主板序列号、MAC地址等硬件信息生成
     *
     * @return 机器码
     */
    public static String getMachineCode() {
        try {
            List<String> hardwareInfo = new ArrayList<>();
            
            // 获取MAC地址
            String macAddress = getMacAddress();
            if (macAddress != null && !macAddress.isEmpty()) {
                hardwareInfo.add("MAC:" + macAddress);
            }
            
            // 获取CPU信息
            String cpuInfo = getCpuInfo();
            if (cpuInfo != null && !cpuInfo.isEmpty()) {
                hardwareInfo.add("CPU:" + cpuInfo);
            }
            
            // 获取主板信息
            String motherboardInfo = getMotherboardInfo();
            if (motherboardInfo != null && !motherboardInfo.isEmpty()) {
                hardwareInfo.add("MB:" + motherboardInfo);
            }
            
            // 获取硬盘信息
            String diskInfo = getDiskInfo();
            if (diskInfo != null && !diskInfo.isEmpty()) {
                hardwareInfo.add("DISK:" + diskInfo);
            }
            
            // 如果没有获取到任何硬件信息，使用系统属性作为备选
            if (hardwareInfo.isEmpty()) {
                hardwareInfo.add("OS:" + System.getProperty("os.name"));
                hardwareInfo.add("ARCH:" + System.getProperty("os.arch"));
                hardwareInfo.add("USER:" + System.getProperty("user.name"));
            }
            
            // 将所有硬件信息组合并生成MD5哈希
            String combined = String.join("|", hardwareInfo);
            return generateMD5(combined);
            
        } catch (Exception e) {
            log.error("获取机器码失败", e);
            // 返回一个基于系统属性的备用机器码
            return generateFallbackMachineCode();
        }
    }

    /**
     * 获取MAC地址
     *
     * @return MAC地址
     */
    private static String getMacAddress() {
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            NetworkInterface networkInterface = NetworkInterface.getByInetAddress(localHost);
            
            if (networkInterface != null) {
                byte[] macBytes = networkInterface.getHardwareAddress();
                if (macBytes != null) {
                    StringBuilder macAddress = new StringBuilder();
                    for (int i = 0; i < macBytes.length; i++) {
                        macAddress.append(String.format("%02X", macBytes[i]));
                        if (i < macBytes.length - 1) {
                            macAddress.append(":");
                        }
                    }
                    return macAddress.toString();
                }
            }
            
            // 如果获取本地主机MAC失败，尝试获取第一个可用的网络接口
            List<NetworkInterface> networkInterfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
            for (NetworkInterface ni : networkInterfaces) {
                if (!ni.isLoopback() && !ni.isVirtual() && ni.isUp()) {
                    byte[] macBytes = ni.getHardwareAddress();
                    if (macBytes != null) {
                        StringBuilder macAddress = new StringBuilder();
                        for (int i = 0; i < macBytes.length; i++) {
                            macAddress.append(String.format("%02X", macBytes[i]));
                            if (i < macBytes.length - 1) {
                                macAddress.append(":");
                            }
                        }
                        return macAddress.toString();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取MAC地址失败", e);
        }
        return null;
    }

    /**
     * 获取CPU信息
     * 在Java中直接获取CPU序列号比较困难，这里使用CPU核心数和架构作为标识
     *
     * @return CPU信息
     */
    private static String getCpuInfo() {
        try {
            int processors = Runtime.getRuntime().availableProcessors();
            String arch = System.getProperty("os.arch");
            return processors + "-" + arch;
        } catch (Exception e) {
            log.warn("获取CPU信息失败", e);
            return null;
        }
    }

    /**
     * 获取主板信息
     * 在Java中直接获取主板序列号比较困难，这里使用系统相关信息作为标识
     *
     * @return 主板信息
     */
    private static String getMotherboardInfo() {
        try {
            String osName = System.getProperty("os.name");
            String osVersion = System.getProperty("os.version");
            return osName + "-" + osVersion;
        } catch (Exception e) {
            log.warn("获取主板信息失败", e);
            return null;
        }
    }

    /**
     * 获取硬盘信息
     * 使用可用空间和总空间作为硬盘标识
     *
     * @return 硬盘信息
     */
    private static String getDiskInfo() {
        try {
            java.io.File root = new java.io.File("/");
            if (System.getProperty("os.name").toLowerCase().contains("windows")) {
                root = new java.io.File("C:");
            }
            
            long totalSpace = root.getTotalSpace();
            return String.valueOf(totalSpace / (1024 * 1024 * 1024)); // GB
        } catch (Exception e) {
            log.warn("获取硬盘信息失败", e);
            return null;
        }
    }

    /**
     * 生成MD5哈希
     *
     * @param input 输入字符串
     * @return MD5哈希值
     */
    private static String generateMD5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes("UTF-8"));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString().toUpperCase();
        } catch (Exception e) {
            log.error("生成MD5哈希失败", e);
            return input.hashCode() + "";
        }
    }

    /**
     * 生成备用机器码
     * 当无法获取硬件信息时使用
     *
     * @return 备用机器码
     */
    private static String generateFallbackMachineCode() {
        String fallback = System.getProperty("user.name") + 
                         System.getProperty("os.name") + 
                         System.getProperty("java.version");
        return generateMD5(fallback);
    }

    /**
     * 验证机器码是否匹配
     *
     * @param expectedMachineCode 期望的机器码
     * @return true-匹配，false-不匹配
     */
    public static boolean validateMachineCode(String expectedMachineCode) {
        if (expectedMachineCode == null || expectedMachineCode.trim().isEmpty()) {
            return false;
        }
        
        String currentMachineCode = getMachineCode();
        return expectedMachineCode.equals(currentMachineCode);
    }
}
