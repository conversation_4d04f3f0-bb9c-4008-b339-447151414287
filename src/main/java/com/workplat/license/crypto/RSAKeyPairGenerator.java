package com.workplat.license.crypto;

import lombok.extern.slf4j.Slf4j;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Base64;

/**
 * RSA密钥对生成器
 * 用于生成License签名所需的RSA公私钥对
 *
 * <AUTHOR> Management Tool
 * @date 2025/08/08
 */
@Slf4j
public class RSAKeyPairGenerator {

    /**
     * 默认密钥长度
     */
    private static final int DEFAULT_KEY_SIZE = 2048;

    /**
     * RSA算法名称
     */
    private static final String ALGORITHM = "RSA";

    /**
     * 生成RSA密钥对
     *
     * @return RSA密钥对
     */
    public static RSAKeyPair generateKeyPair() {
        return generateKeyPair(DEFAULT_KEY_SIZE);
    }

    /**
     * 生成指定长度的RSA密钥对
     *
     * @param keySize 密钥长度（建议1024、2048、4096）
     * @return RSA密钥对
     */
    public static RSAKeyPair generateKeyPair(int keySize) {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(ALGORITHM);
            keyPairGenerator.initialize(keySize);
            KeyPair keyPair = keyPairGenerator.generateKeyPair();

            PublicKey publicKey = keyPair.getPublic();
            PrivateKey privateKey = keyPair.getPrivate();

            // 将密钥转换为Base64编码的字符串
            String publicKeyString = Base64.getEncoder().encodeToString(publicKey.getEncoded());
            String privateKeyString = Base64.getEncoder().encodeToString(privateKey.getEncoded());

            log.info("RSA密钥对生成成功，密钥长度：{} bits", keySize);

            return new RSAKeyPair(publicKeyString, privateKeyString, keySize);

        } catch (Exception e) {
            log.error("生成RSA密钥对失败", e);
            throw new RuntimeException("生成RSA密钥对失败", e);
        }
    }

    /**
     * RSA密钥对数据类
     */
    public static class RSAKeyPair {
        /**
         * 公钥（Base64编码）
         */
        private final String publicKey;

        /**
         * 私钥（Base64编码）
         */
        private final String privateKey;

        /**
         * 密钥长度
         */
        private final int keySize;

        public RSAKeyPair(String publicKey, String privateKey, int keySize) {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
            this.keySize = keySize;
        }

        public String getPublicKey() {
            return publicKey;
        }

        public String getPrivateKey() {
            return privateKey;
        }

        public int getKeySize() {
            return keySize;
        }

        /**
         * 打印密钥对信息（用于调试）
         */
        public void printKeyPair() {
            System.out.println("=== RSA密钥对信息 ===");
            System.out.println("密钥长度: " + keySize + " bits");
            System.out.println("公钥 (用于验证签名):");
            System.out.println(publicKey);
            System.out.println();
            System.out.println("私钥 (用于生成签名，请妥善保管):");
            System.out.println(privateKey);
            System.out.println("==================");
        }

        /**
         * 获取密钥对的字符串表示
         */
        @Override
        public String toString() {
            return String.format("RSAKeyPair{keySize=%d, publicKey='%s...', privateKey='%s...'}",
                    keySize,
                    publicKey.substring(0, Math.min(20, publicKey.length())),
                    privateKey.substring(0, Math.min(20, privateKey.length())));
        }
    }

    /**
     * 主方法，用于生成密钥对（可以作为独立工具使用）
     */
    public static void main(String[] args) {
        try {
            System.out.println("正在生成RSA密钥对...");
            
            // 生成2048位密钥对
            RSAKeyPair keyPair = generateKeyPair(2048);
            
            // 打印密钥对
            keyPair.printKeyPair();
            
            System.out.println("密钥对生成完成！");
            System.out.println("请将公钥保存到应用程序中用于验证License");
            System.out.println("请将私钥妥善保管，用于生成License签名");
            
        } catch (Exception e) {
            System.err.println("生成密钥对失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
