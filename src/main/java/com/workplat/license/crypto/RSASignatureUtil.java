package com.workplat.license.crypto;

import lombok.extern.slf4j.Slf4j;

import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * RSA数字签名工具类
 * 用于License的数字签名生成和验证
 *
 * <AUTHOR> Management Tool
 * @date 2025/08/08
 */
@Slf4j
public class RSASignatureUtil {

    /**
     * RSA算法名称
     */
    private static final String RSA_ALGORITHM = "RSA";

    /**
     * 签名算法
     */
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";

    /**
     * 生成数字签名
     *
     * @param data 要签名的数据
     * @param privateKeyString 私钥（Base64编码）
     * @return 数字签名（Base64编码）
     */
    public static String generateSignature(String data, String privateKeyString) {
        try {
            // 解码私钥
            PrivateKey privateKey = getPrivateKeyFromString(privateKeyString);

            // 创建签名对象
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initSign(privateKey);
            signature.update(data.getBytes("UTF-8"));

            // 生成签名
            byte[] signatureBytes = signature.sign();

            // 返回Base64编码的签名
            String signatureString = Base64.getEncoder().encodeToString(signatureBytes);
            log.debug("数字签名生成成功，数据长度：{}, 签名长度：{}", data.length(), signatureString.length());

            return signatureString;

        } catch (Exception e) {
            log.error("生成数字签名失败", e);
            throw new RuntimeException("生成数字签名失败", e);
        }
    }

    /**
     * 验证数字签名
     *
     * @param data 原始数据
     * @param signatureString 数字签名（Base64编码）
     * @param publicKeyString 公钥（Base64编码）
     * @return true-验证成功，false-验证失败
     */
    public static boolean verifySignature(String data, String signatureString, String publicKeyString) {
        try {
            // 解码公钥
            PublicKey publicKey = getPublicKeyFromString(publicKeyString);

            // 解码签名
            byte[] signatureBytes = Base64.getDecoder().decode(signatureString);

            // 创建验证对象
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initVerify(publicKey);
            signature.update(data.getBytes("UTF-8"));

            // 验证签名
            boolean isValid = signature.verify(signatureBytes);
            log.debug("数字签名验证结果：{}", isValid ? "成功" : "失败");

            return isValid;

        } catch (Exception e) {
            log.error("验证数字签名失败", e);
            return false;
        }
    }

    /**
     * 从Base64字符串获取私钥对象
     *
     * @param privateKeyString 私钥字符串（Base64编码）
     * @return 私钥对象
     */
    private static PrivateKey getPrivateKeyFromString(String privateKeyString) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(privateKeyString);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
            return keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            log.error("解析私钥失败", e);
            throw new RuntimeException("解析私钥失败", e);
        }
    }

    /**
     * 从Base64字符串获取公钥对象
     *
     * @param publicKeyString 公钥字符串（Base64编码）
     * @return 公钥对象
     */
    private static PublicKey getPublicKeyFromString(String publicKeyString) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(publicKeyString);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
            return keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            log.error("解析公钥失败", e);
            throw new RuntimeException("解析公钥失败", e);
        }
    }

    /**
     * 验证密钥对是否匹配
     *
     * @param publicKeyString 公钥（Base64编码）
     * @param privateKeyString 私钥（Base64编码）
     * @return true-匹配，false-不匹配
     */
    public static boolean validateKeyPair(String publicKeyString, String privateKeyString) {
        try {
            String testData = "RSA Key Pair Validation Test Data";
            String signature = generateSignature(testData, privateKeyString);
            return verifySignature(testData, signature, publicKeyString);
        } catch (Exception e) {
            log.error("验证密钥对失败", e);
            return false;
        }
    }

    /**
     * 获取公钥指纹（用于标识公钥）
     *
     * @param publicKeyString 公钥（Base64编码）
     * @return 公钥指纹（MD5哈希）
     */
    public static String getPublicKeyFingerprint(String publicKeyString) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(publicKeyString.getBytes("UTF-8"));

            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString().toUpperCase();
        } catch (Exception e) {
            log.error("生成公钥指纹失败", e);
            return null;
        }
    }
}
