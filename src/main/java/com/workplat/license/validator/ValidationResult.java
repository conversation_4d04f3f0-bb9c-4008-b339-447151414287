package com.workplat.license.validator;

import com.workplat.license.model.LicenseInfo;
import lombok.Data;

/**
 * License验证结果
 * 包含验证状态、消息和License信息
 *
 * <AUTHOR> Management Tool
 * @date 2025/08/09
 */
@Data
public class ValidationResult {

    /**
     * 验证是否成功
     */
    private boolean valid;

    /**
     * 验证结果消息
     */
    private String message;

    /**
     * License信息（验证成功时包含）
     */
    private LicenseInfo licenseInfo;

    /**
     * 错误代码（可选）
     */
    private String errorCode;

    /**
     * 验证时间戳
     */
    private long timestamp;

    /**
     * 默认构造函数
     */
    public ValidationResult() {
        this.valid = false;
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 构造函数
     *
     * @param valid 验证结果
     * @param message 消息
     */
    public ValidationResult(boolean valid, String message) {
        this.valid = valid;
        this.message = message;
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 创建成功的验证结果
     *
     * @param message 成功消息
     * @param licenseInfo License信息
     * @return 验证结果
     */
    public static ValidationResult success(String message, LicenseInfo licenseInfo) {
        ValidationResult result = new ValidationResult(true, message);
        result.setLicenseInfo(licenseInfo);
        return result;
    }

    /**
     * 创建成功的验证结果
     *
     * @param licenseInfo License信息
     * @return 验证结果
     */
    public static ValidationResult success(LicenseInfo licenseInfo) {
        return success("License验证成功", licenseInfo);
    }

    /**
     * 创建失败的验证结果
     *
     * @param message 失败消息
     * @return 验证结果
     */
    public static ValidationResult failure(String message) {
        return new ValidationResult(false, message);
    }

    /**
     * 创建失败的验证结果
     *
     * @param message 失败消息
     * @param errorCode 错误代码
     * @return 验证结果
     */
    public static ValidationResult failure(String message, String errorCode) {
        ValidationResult result = new ValidationResult(false, message);
        result.setErrorCode(errorCode);
        return result;
    }

    /**
     * 检查验证是否成功
     *
     * @return true-成功，false-失败
     */
    public boolean isSuccess() {
        return valid;
    }

    /**
     * 检查验证是否失败
     *
     * @return true-失败，false-成功
     */
    public boolean isFailure() {
        return !valid;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("ValidationResult{");
        sb.append("valid=").append(valid);
        sb.append(", message='").append(message).append('\'');
        if (errorCode != null) {
            sb.append(", errorCode='").append(errorCode).append('\'');
        }
        if (licenseInfo != null) {
            sb.append(", licenseId='").append(licenseInfo.getLicenseId()).append('\'');
        }
        sb.append(", timestamp=").append(timestamp);
        sb.append('}');
        return sb.toString();
    }
}
