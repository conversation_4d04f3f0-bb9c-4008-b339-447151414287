package com.workplat.license.validator;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.workplat.license.crypto.RSASignatureUtil;
import com.workplat.license.model.LicenseInfo;
import com.workplat.license.util.MachineCodeUtil;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;

/**
 * License验证器
 * 用于验证License文件的有效性和权限
 *
 * <AUTHOR> Management Tool
 * @date 2025/08/09
 */
@Slf4j
public class LicenseValidator {

    /**
     * JSON对象映射器
     */
    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.registerModule(new JavaTimeModule());
    }

    /**
     * 验证License文件
     *
     * @param licenseFilePath License文件路径
     * @param publicKey 公钥（用于验证签名）
     * @return 验证结果
     */
    public static ValidationResult validateLicenseFile(String licenseFilePath, String publicKey) {
        try {
            // 1. 读取License文件
            String licenseContent = readLicenseFileContent(licenseFilePath);
            if (licenseContent == null || licenseContent.trim().isEmpty()) {
                return ValidationResult.failure("License文件内容为空或格式错误");
            }

            // 2. 解析License信息
            LicenseInfo licenseInfo = parseLicenseFromContent(licenseContent);
            if (licenseInfo == null) {
                return ValidationResult.failure("License文件解析失败");
            }

            // 3. 验证License
            return validateLicense(licenseInfo, publicKey);

        } catch (Exception e) {
            log.error("验证License文件失败", e);
            return ValidationResult.failure("验证License文件时发生错误: " + e.getMessage());
        }
    }

    /**
     * 验证License对象
     *
     * @param licenseInfo License信息
     * @param publicKey 公钥（用于验证签名）
     * @return 验证结果
     */
    public static ValidationResult validateLicense(LicenseInfo licenseInfo, String publicKey) {
        ValidationResult result = new ValidationResult();

        try {
            // 1. 验证基本信息
            if (!validateBasicInfo(licenseInfo, result)) {
                return result;
            }

            // 2. 验证数字签名
            if (!validateSignature(licenseInfo, publicKey, result)) {
                return result;
            }

            // 3. 验证时间有效性
            if (!validateTimeValidity(licenseInfo, result)) {
                return result;
            }

            // 4. 验证硬件绑定
            if (!validateMachineBinding(licenseInfo, result)) {
                return result;
            }

            // 5. 验证License类型
            if (!validateLicenseType(licenseInfo, result)) {
                return result;
            }

            result.setValid(true);
            result.setMessage("License验证成功");
            result.setLicenseInfo(licenseInfo);

            log.info("License验证成功: {}", licenseInfo.getLicenseId());
            return result;

        } catch (Exception e) {
            log.error("验证License失败", e);
            return ValidationResult.failure("验证License时发生错误: " + e.getMessage());
        }
    }

    /**
     * 验证基本信息
     */
    private static boolean validateBasicInfo(LicenseInfo licenseInfo, ValidationResult result) {
        if (licenseInfo.getLicenseId() == null || licenseInfo.getLicenseId().trim().isEmpty()) {
            result.setMessage("License ID不能为空");
            return false;
        }

        if (licenseInfo.getProductName() == null || licenseInfo.getProductName().trim().isEmpty()) {
            result.setMessage("产品名称不能为空");
            return false;
        }

        if (licenseInfo.getLicenseType() == null) {
            result.setMessage("License类型不能为空");
            return false;
        }

        return true;
    }

    /**
     * 验证数字签名
     */
    private static boolean validateSignature(LicenseInfo licenseInfo, String publicKey, ValidationResult result) {
        try {
            if (publicKey == null || publicKey.trim().isEmpty()) {
                result.setMessage("公钥不能为空");
                return false;
            }

            if (licenseInfo.getSignature() == null || licenseInfo.getSignature().trim().isEmpty()) {
                result.setMessage("License签名不能为空");
                return false;
            }

            // 创建用于验证的License副本（不包含签名）
            LicenseInfo licenseForVerification = cloneLicenseInfo(licenseInfo);
            licenseForVerification.setSignature(null);

            String licenseContent = objectMapper.writeValueAsString(licenseForVerification);
            boolean signatureValid = RSASignatureUtil.verifySignature(
                    licenseContent, 
                    licenseInfo.getSignature(), 
                    publicKey
            );

            if (!signatureValid) {
                result.setMessage("License数字签名验证失败，文件可能被篡改");
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("验证数字签名失败", e);
            result.setMessage("验证数字签名时发生错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 验证时间有效性
     */
    private static boolean validateTimeValidity(LicenseInfo licenseInfo, ValidationResult result) {
        LocalDateTime now = LocalDateTime.now();

        // 检查是否已生效
        if (licenseInfo.getEffectiveDate() != null && now.isBefore(licenseInfo.getEffectiveDate())) {
            result.setMessage("License尚未生效，生效时间: " + licenseInfo.getEffectiveDate());
            return false;
        }

        // 检查是否已过期
        if (licenseInfo.getExpireDate() != null && now.isAfter(licenseInfo.getExpireDate())) {
            result.setMessage("License已过期，过期时间: " + licenseInfo.getExpireDate());
            return false;
        }

        return true;
    }

    /**
     * 验证硬件绑定
     */
    private static boolean validateMachineBinding(LicenseInfo licenseInfo, ValidationResult result) {
        if (licenseInfo.getMachineCode() != null && !licenseInfo.getMachineCode().trim().isEmpty()) {
            String currentMachineCode = MachineCodeUtil.getMachineCode();
            if (!licenseInfo.getMachineCode().equals(currentMachineCode)) {
                result.setMessage("License硬件绑定验证失败，当前机器码不匹配");
                return false;
            }
        }
        return true;
    }

    /**
     * 验证License类型
     */
    private static boolean validateLicenseType(LicenseInfo licenseInfo, ValidationResult result) {
        // 这里可以根据业务需求添加特定的License类型验证逻辑
        // 例如：试用版的特殊限制等
        return true;
    }

    /**
     * 检查是否有指定功能权限
     *
     * @param licenseInfo License信息
     * @param feature 功能名称
     * @return true-有权限，false-无权限
     */
    public static boolean hasFeature(LicenseInfo licenseInfo, String feature) {
        if (licenseInfo == null || feature == null) {
            return false;
        }

        List<String> authorizedFeatures = licenseInfo.getAuthorizedFeatures();
        if (authorizedFeatures == null || authorizedFeatures.isEmpty()) {
            return false;
        }

        return authorizedFeatures.contains(feature) || authorizedFeatures.contains("ALL_FEATURES");
    }

    /**
     * 检查用户数限制
     *
     * @param licenseInfo License信息
     * @param currentUserCount 当前用户数
     * @return true-未超限，false-已超限
     */
    public static boolean checkUserLimit(LicenseInfo licenseInfo, int currentUserCount) {
        if (licenseInfo == null) {
            return false;
        }

        Integer maxUsers = licenseInfo.getMaxUsers();
        if (maxUsers == null || maxUsers == -1) {
            return true; // 无限制
        }

        return currentUserCount <= maxUsers;
    }

    /**
     * 读取License文件内容
     */
    private static String readLicenseFileContent(String filePath) {
        try {
            String content = new String(Files.readAllBytes(Paths.get(filePath)), "UTF-8");

            // 跳过头部注释，获取Base64编码的内容
            String[] lines = content.split("\\r?\\n");
            StringBuilder licenseContent = new StringBuilder();

            for (String line : lines) {
                // 跳过注释行和分隔线
                if (line.startsWith("#")) {
                    continue;
                }
                // 收集非空行作为License内容
                if (!line.trim().isEmpty()) {
                    licenseContent.append(line.trim());
                }
            }

            String result = licenseContent.toString();
            log.debug("读取License文件内容长度: {}", result.length());
            return result;

        } catch (Exception e) {
            log.error("读取License文件失败: {}", filePath, e);
            return null;
        }
    }

    /**
     * 从内容解析License信息
     */
    private static LicenseInfo parseLicenseFromContent(String licenseContent) {
        try {
            // Base64解码
            byte[] decodedBytes = Base64.getDecoder().decode(licenseContent);
            String jsonContent = new String(decodedBytes, StandardCharsets.UTF_8);
            
            // JSON解析
            return objectMapper.readValue(jsonContent, LicenseInfo.class);
            
        } catch (Exception e) {
            log.error("解析License内容失败", e);
            return null;
        }
    }

    /**
     * 克隆License信息对象
     */
    private static LicenseInfo cloneLicenseInfo(LicenseInfo original) {
        try {
            String json = objectMapper.writeValueAsString(original);
            return objectMapper.readValue(json, LicenseInfo.class);
        } catch (Exception e) {
            throw new RuntimeException("克隆License信息失败", e);
        }
    }
}
