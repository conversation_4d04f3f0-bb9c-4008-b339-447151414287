package com.workplat.license.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * License信息实体类
 * 包含完整的软件授权信息
 *
 * <AUTHOR> Management Tool
 * @date 2025/08/08
 */
@Data
public class LicenseInfo {

    /**
     * License唯一标识
     */
    private String licenseId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品版本
     */
    private String productVersion;

    /**
     * 客户信息
     */
    private CustomerInfo customerInfo;

    /**
     * License类型（TRIAL-试用版, STANDARD-标准版, ENTERPRISE-企业版）
     */
    private LicenseType licenseType;

    /**
     * 发行日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime issueDate;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectiveDate;

    /**
     * 过期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireDate;

    /**
     * 硬件指纹（用于硬件绑定）
     */
    private String machineCode;

    /**
     * 授权功能列表
     */
    private List<String> authorizedFeatures;

    /**
     * 最大并发用户数（-1表示无限制）
     */
    private Integer maxUsers;

    /**
     * 扩展属性
     */
    private String extraData;

    /**
     * 数字签名
     */
    private String signature;

    /**
     * License版本号
     */
    private String version = "1.0";

    /**
     * 客户信息内部类
     */
    @Data
    public static class CustomerInfo {
        /**
         * 客户名称
         */
        private String customerName;

        /**
         * 客户代码
         */
        private String customerCode;

        /**
         * 联系人
         */
        private String contactPerson;

        /**
         * 联系电话
         */
        private String contactPhone;

        /**
         * 联系邮箱
         */
        private String contactEmail;

        /**
         * 公司地址
         */
        private String address;
    }

    /**
     * License类型枚举
     */
    public enum LicenseType {
        /**
         * 试用版
         */
        TRIAL("试用版"),

        /**
         * 标准版
         */
        STANDARD("标准版"),

        /**
         * 企业版
         */
        ENTERPRISE("企业版"),

        /**
         * 开发版
         */
        DEVELOPMENT("开发版");

        private final String description;

        LicenseType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查License是否已过期
     *
     * @return true-已过期，false-未过期
     */
    public boolean isExpired() {
        if (expireDate == null) {
            return false;
        }
        return LocalDateTime.now().isAfter(expireDate);
    }

    /**
     * 检查License是否已生效
     *
     * @return true-已生效，false-未生效
     */
    public boolean isEffective() {
        if (effectiveDate == null) {
            return true;
        }
        return LocalDateTime.now().isAfter(effectiveDate);
    }

    /**
     * 获取剩余天数
     *
     * @return 剩余天数，负数表示已过期
     */
    public long getRemainingDays() {
        if (expireDate == null) {
            return Long.MAX_VALUE;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(expireDate)) {
            return -java.time.Duration.between(expireDate, now).toDays();
        } else {
            return java.time.Duration.between(now, expireDate).toDays();
        }
    }

    /**
     * 检查是否包含指定功能
     *
     * @param feature 功能名称
     * @return true-包含，false-不包含
     */
    public boolean hasFeature(String feature) {
        return authorizedFeatures != null && authorizedFeatures.contains(feature);
    }
}
