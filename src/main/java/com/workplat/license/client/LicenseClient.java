package com.workplat.license.client;

import com.workplat.license.manager.LicenseManager;
import com.workplat.license.model.LicenseInfo;
import com.workplat.license.validator.ValidationResult;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

/**
 * License客户端
 * 提供简化的License验证和权限检查API，方便其他项目集成
 *
 * <AUTHOR> Management Tool
 * @date 2025/08/09
 */
@Slf4j
public class LicenseClient {

    /**
     * License管理器实例
     */
    private final LicenseManager licenseManager;

    /**
     * 是否已初始化
     */
    private boolean initialized = false;

    /**
     * 构造函数
     */
    public LicenseClient() {
        this.licenseManager = LicenseManager.getInstance();
    }

    /**
     * 从配置文件初始化License客户端
     *
     * @param configPath 配置文件路径（classpath相对路径）
     * @return 初始化结果
     */
    public ValidationResult initializeFromConfig(String configPath) {
        try {
            Properties config = loadConfig(configPath);
            
            String licenseFilePath = config.getProperty("license.file.path");
            String publicKey = config.getProperty("license.public.key");
            
            if (licenseFilePath == null || licenseFilePath.trim().isEmpty()) {
                return ValidationResult.failure("配置文件中未找到license.file.path");
            }
            
            if (publicKey == null || publicKey.trim().isEmpty()) {
                return ValidationResult.failure("配置文件中未找到license.public.key");
            }
            
            return initialize(licenseFilePath, publicKey);
            
        } catch (Exception e) {
            log.error("从配置文件初始化License客户端失败", e);
            return ValidationResult.failure("从配置文件初始化失败: " + e.getMessage());
        }
    }

    /**
     * 从资源文件初始化License客户端
     *
     * @param licenseResourcePath License文件资源路径
     * @param publicKeyResourcePath 公钥文件资源路径
     * @return 初始化结果
     */
    public ValidationResult initializeFromResources(String licenseResourcePath, String publicKeyResourcePath) {
        try {
            // 从资源文件读取License文件路径（实际部署时的路径）
            String licenseFilePath = getResourceAsString(licenseResourcePath);
            String publicKey = getResourceAsString(publicKeyResourcePath);
            
            return initialize(licenseFilePath.trim(), publicKey.trim());
            
        } catch (Exception e) {
            log.error("从资源文件初始化License客户端失败", e);
            return ValidationResult.failure("从资源文件初始化失败: " + e.getMessage());
        }
    }

    /**
     * 直接初始化License客户端
     *
     * @param licenseFilePath License文件路径
     * @param publicKey 公钥内容
     * @return 初始化结果
     */
    public ValidationResult initialize(String licenseFilePath, String publicKey) {
        try {
            ValidationResult result = licenseManager.initialize(licenseFilePath, publicKey);
            this.initialized = result.isSuccess();
            
            if (initialized) {
                log.info("License客户端初始化成功");
            } else {
                log.error("License客户端初始化失败: {}", result.getMessage());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("初始化License客户端失败", e);
            return ValidationResult.failure("初始化失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否有指定功能权限
     *
     * @param feature 功能名称
     * @return true-有权限，false-无权限
     * @throws LicenseException 如果License未初始化或无效
     */
    public boolean hasFeature(String feature) throws LicenseException {
        checkInitialized();
        return licenseManager.hasFeature(feature);
    }

    /**
     * 检查用户数限制
     *
     * @param currentUserCount 当前用户数
     * @return true-未超限，false-已超限
     * @throws LicenseException 如果License未初始化或无效
     */
    public boolean checkUserLimit(int currentUserCount) throws LicenseException {
        checkInitialized();
        return licenseManager.checkUserLimit(currentUserCount);
    }

    /**
     * 获取当前License信息
     *
     * @return License信息
     * @throws LicenseException 如果License未初始化或无效
     */
    public LicenseInfo getLicenseInfo() throws LicenseException {
        checkInitialized();
        return licenseManager.getCurrentLicense();
    }

    /**
     * 检查License是否有效
     *
     * @return true-有效，false-无效
     */
    public boolean isLicenseValid() {
        if (!initialized) {
            return false;
        }
        return licenseManager.isLicenseValid();
    }

    /**
     * 重新加载License
     *
     * @return 验证结果
     * @throws LicenseException 如果License未初始化
     */
    public ValidationResult reloadLicense() throws LicenseException {
        if (!initialized) {
            throw new LicenseException("License客户端未初始化");
        }
        return licenseManager.reloadLicense();
    }

    /**
     * 获取License类型
     *
     * @return License类型
     * @throws LicenseException 如果License未初始化或无效
     */
    public LicenseInfo.LicenseType getLicenseType() throws LicenseException {
        LicenseInfo licenseInfo = getLicenseInfo();
        return licenseInfo != null ? licenseInfo.getLicenseType() : null;
    }

    /**
     * 获取最大用户数
     *
     * @return 最大用户数（-1表示无限制）
     * @throws LicenseException 如果License未初始化或无效
     */
    public int getMaxUsers() throws LicenseException {
        LicenseInfo licenseInfo = getLicenseInfo();
        return licenseInfo != null ? licenseInfo.getMaxUsers() : 0;
    }

    /**
     * 检查是否为试用版License
     *
     * @return true-试用版，false-非试用版
     * @throws LicenseException 如果License未初始化或无效
     */
    public boolean isTrialLicense() throws LicenseException {
        return getLicenseType() == LicenseInfo.LicenseType.TRIAL;
    }

    /**
     * 设置定时验证间隔
     *
     * @param intervalMinutes 间隔分钟数
     */
    public void setValidationInterval(int intervalMinutes) {
        licenseManager.setValidationInterval(intervalMinutes);
    }

    /**
     * 启用或禁用定时验证
     *
     * @param enable true-启用，false-禁用
     */
    public void setPeriodicValidationEnabled(boolean enable) {
        licenseManager.setPeriodicValidationEnabled(enable);
    }

    /**
     * 关闭License客户端
     */
    public void shutdown() {
        licenseManager.shutdown();
        initialized = false;
        log.info("License客户端已关闭");
    }

    /**
     * 检查是否已初始化
     */
    private void checkInitialized() throws LicenseException {
        if (!initialized) {
            throw new LicenseException("License客户端未初始化，请先调用initialize方法");
        }
        
        if (!licenseManager.isLicenseValid()) {
            throw new LicenseException("License无效或已过期");
        }
    }

    /**
     * 加载配置文件
     */
    private Properties loadConfig(String configPath) throws Exception {
        Properties properties = new Properties();
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(configPath)) {
            if (inputStream == null) {
                throw new Exception("配置文件未找到: " + configPath);
            }
            properties.load(inputStream);
        }
        return properties;
    }

    /**
     * 从资源文件读取字符串内容
     */
    private String getResourceAsString(String resourcePath) throws Exception {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(resourcePath)) {
            if (inputStream == null) {
                throw new Exception("资源文件未找到: " + resourcePath);
            }
            
            byte[] bytes = inputStream.readAllBytes();
            return new String(bytes, StandardCharsets.UTF_8);
        }
    }

    /**
     * License异常类
     */
    public static class LicenseException extends Exception {
        public LicenseException(String message) {
            super(message);
        }
        
        public LicenseException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
