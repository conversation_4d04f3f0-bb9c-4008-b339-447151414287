package com.workplat.license.generator;

import com.workplat.license.model.LicenseInfo;
import com.workplat.license.util.MachineCodeUtil;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * License构建器
 * 提供流式API来构建License信息
 *
 * <AUTHOR> Management Tool
 * @date 2025/08/08
 */
public class LicenseBuilder {

    private final LicenseInfo licenseInfo;

    private LicenseBuilder() {
        this.licenseInfo = new LicenseInfo();
        // 设置默认值
        this.licenseInfo.setLicenseId(UUID.randomUUID().toString());
        this.licenseInfo.setIssueDate(LocalDateTime.now());
        this.licenseInfo.setVersion("1.0");
        this.licenseInfo.setMaxUsers(-1); // 默认无限制
    }

    /**
     * 创建License构建器
     *
     * @return License构建器实例
     */
    public static LicenseBuilder create() {
        return new LicenseBuilder();
    }

    /**
     * 设置License ID
     *
     * @param licenseId License ID
     * @return 构建器实例
     */
    public LicenseBuilder licenseId(String licenseId) {
        this.licenseInfo.setLicenseId(licenseId);
        return this;
    }

    /**
     * 设置产品信息
     *
     * @param productName 产品名称
     * @param productVersion 产品版本
     * @return 构建器实例
     */
    public LicenseBuilder product(String productName, String productVersion) {
        this.licenseInfo.setProductName(productName);
        this.licenseInfo.setProductVersion(productVersion);
        return this;
    }

    /**
     * 设置客户信息
     *
     * @param customerName 客户名称
     * @param customerCode 客户代码
     * @return 构建器实例
     */
    public LicenseBuilder customer(String customerName, String customerCode) {
        LicenseInfo.CustomerInfo customerInfo = new LicenseInfo.CustomerInfo();
        customerInfo.setCustomerName(customerName);
        customerInfo.setCustomerCode(customerCode);
        this.licenseInfo.setCustomerInfo(customerInfo);
        return this;
    }

    /**
     * 设置详细客户信息
     *
     * @param customerName 客户名称
     * @param customerCode 客户代码
     * @param contactPerson 联系人
     * @param contactPhone 联系电话
     * @param contactEmail 联系邮箱
     * @param address 地址
     * @return 构建器实例
     */
    public LicenseBuilder customer(String customerName, String customerCode, 
                                  String contactPerson, String contactPhone, 
                                  String contactEmail, String address) {
        LicenseInfo.CustomerInfo customerInfo = new LicenseInfo.CustomerInfo();
        customerInfo.setCustomerName(customerName);
        customerInfo.setCustomerCode(customerCode);
        customerInfo.setContactPerson(contactPerson);
        customerInfo.setContactPhone(contactPhone);
        customerInfo.setContactEmail(contactEmail);
        customerInfo.setAddress(address);
        this.licenseInfo.setCustomerInfo(customerInfo);
        return this;
    }

    /**
     * 设置License类型
     *
     * @param licenseType License类型
     * @return 构建器实例
     */
    public LicenseBuilder type(LicenseInfo.LicenseType licenseType) {
        this.licenseInfo.setLicenseType(licenseType);
        return this;
    }

    /**
     * 设置生效日期
     *
     * @param effectiveDate 生效日期
     * @return 构建器实例
     */
    public LicenseBuilder effectiveDate(LocalDateTime effectiveDate) {
        this.licenseInfo.setEffectiveDate(effectiveDate);
        return this;
    }

    /**
     * 设置过期日期
     *
     * @param expireDate 过期日期
     * @return 构建器实例
     */
    public LicenseBuilder expireDate(LocalDateTime expireDate) {
        this.licenseInfo.setExpireDate(expireDate);
        return this;
    }

    /**
     * 设置有效期（从现在开始计算）
     *
     * @param days 有效天数
     * @return 构建器实例
     */
    public LicenseBuilder validForDays(int days) {
        this.licenseInfo.setEffectiveDate(LocalDateTime.now());
        this.licenseInfo.setExpireDate(LocalDateTime.now().plusDays(days));
        return this;
    }

    /**
     * 设置有效期（从指定日期开始计算）
     *
     * @param startDate 开始日期
     * @param days 有效天数
     * @return 构建器实例
     */
    public LicenseBuilder validForDays(LocalDateTime startDate, int days) {
        this.licenseInfo.setEffectiveDate(startDate);
        this.licenseInfo.setExpireDate(startDate.plusDays(days));
        return this;
    }

    /**
     * 绑定当前机器
     *
     * @return 构建器实例
     */
    public LicenseBuilder bindCurrentMachine() {
        String machineCode = MachineCodeUtil.getMachineCode();
        this.licenseInfo.setMachineCode(machineCode);
        return this;
    }

    /**
     * 绑定指定机器码
     *
     * @param machineCode 机器码
     * @return 构建器实例
     */
    public LicenseBuilder bindMachine(String machineCode) {
        this.licenseInfo.setMachineCode(machineCode);
        return this;
    }

    /**
     * 设置授权功能
     *
     * @param features 功能列表
     * @return 构建器实例
     */
    public LicenseBuilder features(String... features) {
        this.licenseInfo.setAuthorizedFeatures(Arrays.asList(features));
        return this;
    }

    /**
     * 设置授权功能
     *
     * @param features 功能列表
     * @return 构建器实例
     */
    public LicenseBuilder features(List<String> features) {
        this.licenseInfo.setAuthorizedFeatures(new ArrayList<>(features));
        return this;
    }

    /**
     * 添加授权功能
     *
     * @param feature 功能名称
     * @return 构建器实例
     */
    public LicenseBuilder addFeature(String feature) {
        if (this.licenseInfo.getAuthorizedFeatures() == null) {
            this.licenseInfo.setAuthorizedFeatures(new ArrayList<>());
        }
        this.licenseInfo.getAuthorizedFeatures().add(feature);
        return this;
    }

    /**
     * 设置最大用户数
     *
     * @param maxUsers 最大用户数（-1表示无限制）
     * @return 构建器实例
     */
    public LicenseBuilder maxUsers(int maxUsers) {
        this.licenseInfo.setMaxUsers(maxUsers);
        return this;
    }

    /**
     * 设置扩展数据
     *
     * @param extraData 扩展数据
     * @return 构建器实例
     */
    public LicenseBuilder extraData(String extraData) {
        this.licenseInfo.setExtraData(extraData);
        return this;
    }

    /**
     * 构建License信息
     *
     * @return License信息
     */
    public LicenseInfo build() {
        // 验证必要字段
        if (licenseInfo.getProductName() == null || licenseInfo.getProductName().trim().isEmpty()) {
            throw new IllegalStateException("产品名称不能为空");
        }
        if (licenseInfo.getExpireDate() == null) {
            throw new IllegalStateException("过期日期不能为空");
        }
        if (licenseInfo.getCustomerInfo() == null) {
            throw new IllegalStateException("客户信息不能为空");
        }

        return licenseInfo;
    }

    /**
     * 创建试用版License
     *
     * @param productName 产品名称
     * @param customerName 客户名称
     * @param trialDays 试用天数
     * @return License构建器
     */
    public static LicenseBuilder createTrialLicense(String productName, String customerName, int trialDays) {
        return create()
                .product(productName, "1.0")
                .customer(customerName, "TRIAL_" + System.currentTimeMillis())
                .type(LicenseInfo.LicenseType.TRIAL)
                .validForDays(trialDays)
                .bindCurrentMachine()
                .features("BASIC_FEATURES");
    }

    /**
     * 创建标准版License
     *
     * @param productName 产品名称
     * @param customerName 客户名称
     * @param customerCode 客户代码
     * @param validDays 有效天数
     * @return License构建器
     */
    public static LicenseBuilder createStandardLicense(String productName, String customerName, 
                                                      String customerCode, int validDays) {
        return create()
                .product(productName, "1.0")
                .customer(customerName, customerCode)
                .type(LicenseInfo.LicenseType.STANDARD)
                .validForDays(validDays)
                .features("ALL_FEATURES");
    }
}
