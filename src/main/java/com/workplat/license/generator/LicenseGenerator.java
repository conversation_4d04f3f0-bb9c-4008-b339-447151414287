package com.workplat.license.generator;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.workplat.license.crypto.RSASignatureUtil;
import com.workplat.license.model.LicenseInfo;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileWriter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

/**
 * License生成器
 * 用于生成加密的License文件
 *
 * <AUTHOR> Management Tool
 * @date 2025/08/08
 */
@Slf4j
public class LicenseGenerator {

    /**
     * License文件扩展名
     */
    private static final String LICENSE_FILE_EXTENSION = ".lic";

    /**
     * JSON对象映射器
     */
    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.registerModule(new JavaTimeModule());
    }

    /**
     * 生成License文件
     *
     * @param licenseInfo License信息
     * @param privateKey 私钥（用于签名）
     * @param outputPath 输出文件路径
     * @return 生成的License文件路径
     */
    public static String generateLicenseFile(LicenseInfo licenseInfo, String privateKey, String outputPath) {
        try {
            // 1. 验证输入参数
            validateInputs(licenseInfo, privateKey, outputPath);

            // 2. 生成License内容的JSON字符串（不包含签名）
            LicenseInfo licenseForSigning = cloneLicenseInfo(licenseInfo);
            licenseForSigning.setSignature(null); // 确保签名字段为空
            String licenseContent = objectMapper.writeValueAsString(licenseForSigning);

            // 3. 生成数字签名
            String signature = RSASignatureUtil.generateSignature(licenseContent, privateKey);
            licenseInfo.setSignature(signature);

            // 4. 生成完整的License JSON（包含签名）
            String completeLicenseJson = objectMapper.writeValueAsString(licenseInfo);

            // 5. Base64编码（简单的内容混淆）
            String encodedLicense = Base64.getEncoder().encodeToString(completeLicenseJson.getBytes("UTF-8"));

            // 6. 生成License文件头部信息
            String licenseFileContent = generateLicenseFileHeader() + "\n" + encodedLicense;

            // 7. 写入文件
            String finalOutputPath = ensureLicenseExtension(outputPath);
            writeToFile(licenseFileContent, finalOutputPath);

            log.info("License文件生成成功: {}", finalOutputPath);
            return finalOutputPath;

        } catch (Exception e) {
            log.error("生成License文件失败", e);
            throw new RuntimeException("生成License文件失败", e);
        }
    }

    /**
     * 生成License文件（自动生成文件名）
     *
     * @param licenseInfo License信息
     * @param privateKey 私钥
     * @param outputDir 输出目录
     * @return 生成的License文件路径
     */
    public static String generateLicenseFile(LicenseInfo licenseInfo, String privateKey, File outputDir) {
        String fileName = generateLicenseFileName(licenseInfo);
        String outputPath = new File(outputDir, fileName).getAbsolutePath();
        return generateLicenseFile(licenseInfo, privateKey, outputPath);
    }

    /**
     * 验证输入参数
     */
    private static void validateInputs(LicenseInfo licenseInfo, String privateKey, String outputPath) {
        if (licenseInfo == null) {
            throw new IllegalArgumentException("License信息不能为空");
        }
        if (privateKey == null || privateKey.trim().isEmpty()) {
            throw new IllegalArgumentException("私钥不能为空");
        }
        if (outputPath == null || outputPath.trim().isEmpty()) {
            throw new IllegalArgumentException("输出路径不能为空");
        }
        if (licenseInfo.getProductName() == null || licenseInfo.getProductName().trim().isEmpty()) {
            throw new IllegalArgumentException("产品名称不能为空");
        }
        if (licenseInfo.getExpireDate() == null) {
            throw new IllegalArgumentException("过期时间不能为空");
        }
    }

    /**
     * 克隆License信息（用于签名）
     */
    private static LicenseInfo cloneLicenseInfo(LicenseInfo original) {
        try {
            String json = objectMapper.writeValueAsString(original);
            return objectMapper.readValue(json, LicenseInfo.class);
        } catch (Exception e) {
            throw new RuntimeException("克隆License信息失败", e);
        }
    }

    /**
     * 生成License文件头部信息
     */
    private static String generateLicenseFileHeader() {
        return "# AI Central Platform License File\n" +
               "# Generated at: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n" +
               "# Warning: Do not modify this file manually\n" +
               "# ==========================================";
    }

    /**
     * 确保文件路径有正确的扩展名
     */
    private static String ensureLicenseExtension(String path) {
        if (!path.toLowerCase().endsWith(LICENSE_FILE_EXTENSION)) {
            return path + LICENSE_FILE_EXTENSION;
        }
        return path;
    }

    /**
     * 生成License文件名
     */
    private static String generateLicenseFileName(LicenseInfo licenseInfo) {
        String customerCode = licenseInfo.getCustomerInfo() != null && 
                             licenseInfo.getCustomerInfo().getCustomerCode() != null ?
                             licenseInfo.getCustomerInfo().getCustomerCode() : "UNKNOWN";
        
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        return String.format("%s_%s_%s%s", 
                licenseInfo.getProductName().replaceAll("[^a-zA-Z0-9]", ""),
                customerCode.replaceAll("[^a-zA-Z0-9]", ""),
                timestamp,
                LICENSE_FILE_EXTENSION);
    }

    /**
     * 写入文件
     */
    private static void writeToFile(String content, String filePath) {
        try {
            // 确保目录存在
            File file = new File(filePath);
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            // 写入文件
            try (FileWriter writer = new FileWriter(file)) {
                writer.write(content);
            }

            log.debug("License文件写入成功: {}, 大小: {} bytes", filePath, file.length());

        } catch (Exception e) {
            log.error("写入License文件失败: {}", filePath, e);
            throw new RuntimeException("写入License文件失败", e);
        }
    }

    /**
     * 读取License文件内容
     *
     * @param filePath License文件路径
     * @return License文件内容（Base64编码的部分）
     */
    public static String readLicenseFileContent(String filePath) {
        try {
            String content = new String(Files.readAllBytes(Paths.get(filePath)), "UTF-8");
            
            // 跳过头部注释，获取Base64编码的内容
            String[] lines = content.split("\n");
            StringBuilder licenseContent = new StringBuilder();
            boolean headerPassed = false;
            
            for (String line : lines) {
                if (line.startsWith("#")) {
                    continue; // 跳过注释行
                }
                if (line.contains("=")) {
                    headerPassed = true;
                    continue; // 跳过分隔线
                }
                if (headerPassed && !line.trim().isEmpty()) {
                    licenseContent.append(line.trim());
                }
            }
            
            return licenseContent.toString();
            
        } catch (Exception e) {
            log.error("读取License文件失败: {}", filePath, e);
            throw new RuntimeException("读取License文件失败", e);
        }
    }

    /**
     * 从License文件内容解析License信息
     *
     * @param licenseFileContent License文件内容（Base64编码）
     * @return License信息
     */
    public static LicenseInfo parseLicenseFromContent(String licenseFileContent) {
        try {
            // Base64解码
            byte[] decodedBytes = Base64.getDecoder().decode(licenseFileContent);
            String licenseJson = new String(decodedBytes, "UTF-8");
            
            // JSON反序列化
            return objectMapper.readValue(licenseJson, LicenseInfo.class);
            
        } catch (Exception e) {
            log.error("解析License内容失败", e);
            throw new RuntimeException("解析License内容失败", e);
        }
    }
}
