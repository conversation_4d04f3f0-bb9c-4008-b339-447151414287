# 客户场景使用示例

## 场景1：软件试用申请

### 客户背景
- 公司：上海创新科技有限公司
- 联系人：李经理
- 需求：30天试用版本，基础功能

### 处理流程

1. **获取客户机器码**
   ```
   # 客户在目标机器上运行工具
   java -jar license-management-tool-1.0.0.jar
   
   # 选择菜单 4
   === 当前机器码 ===
   机器码: B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7
   ```

2. **生成试用License**
   ```
   # 选择菜单 3
   客户名称: 上海创新科技有限公司
   试用天数: 30
   私钥: [输入私钥]
   
   # 生成文件
   试用License生成成功: ./AI中央平台_TRIAL_1641628800000_20250808.lic
   ```

3. **交付给客户**
   - 发送License文件
   - 提供部署说明
   - 设置试用期提醒

## 场景2：正式客户购买

### 客户背景
- 公司：北京大型企业集团
- 联系人：张总监
- 需求：标准版，50用户，1年期

### 处理流程

1. **收集客户信息**
   ```
   客户名称: 北京大型企业集团
   客户代码: *********
   联系人: 张总监
   电话: 13800138000
   邮箱: <EMAIL>
   地址: 北京市朝阳区商务中心
   ```

2. **获取目标机器码**
   ```
   # 客户提供生产环境机器码
   机器码: C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8
   ```

3. **生成正式License**
   ```
   # 选择菜单 2
   产品名称: AI中央平台
   产品版本: 1.0
   客户名称: 北京大型企业集团
   客户代码: *********
   License类型: 2 (标准版)
   有效期: 365天
   机器码: C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8
   授权功能: BASIC_FEATURES,REPORT_FEATURES,EXPORT_FEATURES
   最大用户数: 50
   ```

## 场景3：企业级客户升级

### 客户背景
- 公司：深圳科技巨头
- 当前：标准版客户
- 需求：升级到企业版，无用户限制

### 处理流程

1. **查看当前License**
   ```
   # 选择菜单 5
   License ID: xxx-xxx-xxx
   客户名称: 深圳科技巨头
   License类型: 标准版
   剩余天数: 180
   最大用户数: 100
   授权功能: [BASIC_FEATURES, REPORT_FEATURES]
   ```

2. **生成企业版License**
   ```
   # 保持相同的客户信息和机器码
   # 升级License类型和功能权限
   
   客户名称: 深圳科技巨头
   客户代码: *********
   License类型: 3 (企业版)
   有效期: 365天 (重新计算)
   机器码: [使用相同机器码]
   授权功能: ALL_FEATURES,ADMIN_FEATURES,ENTERPRISE_FEATURES,API_ACCESS
   最大用户数: -1 (无限制)
   ```

## 场景4：多环境部署

### 客户背景
- 公司：全国连锁企业
- 需求：开发、测试、生产三套环境
- 要求：不同环境不同权限

### 处理流程

1. **开发环境License**
   ```
   客户名称: 全国连锁企业-开发环境
   客户代码: DEV2025001
   License类型: 4 (开发版)
   有效期: 180天
   机器码: DEV_MACHINE_CODE
   授权功能: ALL_FEATURES,DEBUG_FEATURES
   最大用户数: 10
   ```

2. **测试环境License**
   ```
   客户名称: 全国连锁企业-测试环境
   客户代码: TEST2025001
   License类型: 2 (标准版)
   有效期: 365天
   机器码: TEST_MACHINE_CODE
   授权功能: BASIC_FEATURES,REPORT_FEATURES
   最大用户数: 20
   ```

3. **生产环境License**
   ```
   客户名称: 全国连锁企业-生产环境
   客户代码: PROD2025001
   License类型: 3 (企业版)
   有效期: 365天
   机器码: PROD_MACHINE_CODE
   授权功能: ALL_FEATURES,ENTERPRISE_FEATURES
   最大用户数: -1
   ```

## 场景5：License续期

### 客户背景
- 公司：老客户续期
- 当前License：即将到期
- 需求：续期并升级功能

### 处理流程

1. **检查当前License状态**
   ```
   剩余天数: 15
   状态: 即将过期
   当前功能: BASIC_FEATURES
   ```

2. **生成续期License**
   ```
   # 使用相同的客户信息
   # 保持机器码不变
   # 更新有效期和功能
   
   有效期: 365天 (重新开始计算)
   授权功能: BASIC_FEATURES,REPORT_FEATURES,NEW_FEATURES
   备注: "续期升级，新增报表和新功能模块"
   ```

## 场景6：紧急License处理

### 客户背景
- 紧急情况：客户License意外损坏
- 时间要求：立即恢复
- 约束条件：无法获取原始信息

### 处理流程

1. **快速信息收集**
   ```
   # 从客户支持记录中获取
   客户代码: 从CRM系统查询
   机器码: 客户重新提供
   License类型: 根据合同确定
   ```

2. **紧急License生成**
   ```
   # 生成临时License
   有效期: 30天 (临时)
   备注: "紧急恢复License，请尽快提供完整信息"
   ```

3. **后续处理**
   ```
   # 收集完整信息后生成正式License
   # 替换临时License
   ```

## 场景7：批量客户管理

### 客户背景
- 渠道商批量采购
- 需要为多个最终客户生成License
- 要求统一管理

### 处理流程

1. **准备客户清单**
   ```csv
   客户名称,客户代码,License类型,有效期,机器码,功能权限
   客户A,A001,STANDARD,365,MAC001,BASIC_FEATURES
   客户B,B001,ENTERPRISE,730,MAC002,ALL_FEATURES
   客户C,C001,TRIAL,30,MAC003,BASIC_FEATURES
   ```

2. **批量生成脚本**
   ```java
   // 使用编程方式批量生成
   for (CustomerInfo customer : customerList) {
       LicenseInfo license = LicenseBuilder.create()
           .customer(customer.getName(), customer.getCode())
           .type(customer.getLicenseType())
           .validForDays(customer.getValidDays())
           .bindMachine(customer.getMachineCode())
           .features(customer.getFeatures())
           .build();
       
       String filePath = LicenseGenerator.generateLicenseFile(
           license, privateKey, outputDir);
   }
   ```

## 最佳实践总结

1. **客户信息管理**
   - 建立客户信息数据库
   - 记录License发放历史
   - 跟踪License使用状态

2. **机器码管理**
   - 提供机器码获取工具
   - 建立机器码变更流程
   - 处理硬件升级场景

3. **License生命周期**
   - 设置到期提醒机制
   - 建立续期处理流程
   - 提供紧急恢复方案

4. **安全管理**
   - 私钥安全存储
   - License文件传输加密
   - 访问权限控制
