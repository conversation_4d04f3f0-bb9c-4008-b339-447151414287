# License管理工具快速入门示例

## 场景1：为新客户生成标准版License

### 步骤1：生成密钥对（首次使用）

运行工具后选择菜单 `1. 生成RSA密钥对`

```
请输入密钥长度（默认2048）: [直接回车使用默认值]
密钥对生成成功！

=== RSA密钥对信息 ===
密钥长度: 2048 bits
公钥 (用于验证签名):
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...

私钥 (用于生成签名，请妥善保管):
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
==================

是否保存到文件？(y/n) [y]: y
密钥对已保存:
公钥文件: public_key_20250808_143022.txt
私钥文件: private_key_20250808_143022.txt
```

### 步骤2：配置公钥到主应用

将生成的公钥配置到AI中央平台的Nacos配置中：

```yaml
license:
  validation-enabled: true
  public-key: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA..."
  product-name: "AI中央平台"
  file-path: "./license.lic"
```

### 步骤3：生成客户License

选择菜单 `2. 生成License文件`

```
产品名称 [AI中央平台]: [直接回车]
产品版本 [1.0]: [直接回车]
客户名称: 北京科技有限公司
客户代码: *********

License类型：
1. 试用版 (TRIAL)
2. 标准版 (STANDARD)
3. 企业版 (ENTERPRISE)
4. 开发版 (DEVELOPMENT)
请选择License类型 [2]: [直接回车选择标准版]

有效期（天数） [365]: [直接回车，一年有效期]

是否绑定当前机器？(y/n) [y]: n
是否指定机器码？(y/n) [n]: y
请输入机器码: A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6

授权功能（逗号分隔，默认ALL_FEATURES） [ALL_FEATURES]: BASIC_FEATURES,REPORT_FEATURES

请输入私钥（用于签名）: [粘贴之前保存的私钥内容]

输出目录 [./]: [直接回车]

License文件生成成功: ./AI中央平台_*********_20250808.lic
```

## 场景2：生成试用License

选择菜单 `3. 生成试用License`

```
客户名称: 上海测试公司
试用天数 [30]: 15
请输入私钥（用于签名）: [粘贴私钥内容]
输出目录 [./]: [直接回车]

试用License生成成功: ./AI中央平台_TRIAL_1641628800000_20250808.lic
```

## 场景3：查看客户机器码

当客户需要硬件绑定时，先获取客户机器的机器码。

选择菜单 `4. 查看当前机器码`

```
=== 当前机器码 ===
机器码: A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6
生成时间: 2025-08-08 14:35:22
说明: 此机器码用于License硬件绑定
```

将此机器码提供给License生成方，用于生成绑定该机器的License。

## 场景4：验证License文件

选择菜单 `5. 查看License信息`

```
请输入License文件路径: ./AI中央平台_*********_20250808.lic

License ID: 550e8400-e29b-41d4-a716-446655440000
产品名称: AI中央平台
产品版本: 1.0
客户名称: 北京科技有限公司
客户代码: *********
License类型: 标准版
发行时间: 2025-08-08T14:30:22
生效时间: 2025-08-08T14:30:22
过期时间: 2026-08-08T14:30:22
剩余天数: 365
最大用户数: -1
授权功能: [BASIC_FEATURES, REPORT_FEATURES]
机器码: A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6
```

## 场景5：批量生成License

### 创建批量生成脚本

```bash
#!/bin/bash
# batch_generate.sh

# 客户信息列表
customers=(
    "客户A,CUST001,365,BASIC_FEATURES"
    "客户B,CUST002,180,ALL_FEATURES"
    "客户C,CUST003,90,TRIAL_FEATURES"
)

# 私钥文件路径
PRIVATE_KEY_FILE="private_key_20250808_143022.txt"
PRIVATE_KEY=$(cat $PRIVATE_KEY_FILE)

# 输出目录
OUTPUT_DIR="./licenses"
mkdir -p $OUTPUT_DIR

echo "开始批量生成License..."

for customer in "${customers[@]}"; do
    IFS=',' read -r name code days features <<< "$customer"
    echo "正在为 $name 生成License..."
    
    # 这里需要调用Java程序的API或者准备输入文件
    # 实际使用时可以通过程序化方式调用LicenseBuilder
done

echo "批量生成完成！"
```

## 场景6：License续期

当客户License即将过期时，可以生成新的License文件：

```
# 使用相同的客户信息和机器码
# 延长有效期到新的日期
# 保持或更新功能权限

产品名称 [AI中央平台]: [直接回车]
产品版本 [1.0]: [直接回车]
客户名称: 北京科技有限公司
客户代码: *********
License类型: 2 [标准版]
有效期（天数）: 365 [再续一年]
机器码: A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6 [使用相同机器码]
授权功能: ALL_FEATURES [升级功能权限]
```

## 常见问题处理

### 问题1：机器码不匹配

```
# 客户反馈License无法使用，提示机器码不匹配
# 解决方案：重新获取客户机器码

1. 让客户在目标机器上运行工具
2. 选择菜单 4 查看机器码
3. 使用新机器码重新生成License
```

### 问题2：License文件损坏

```
# 客户反馈License文件无法解析
# 解决方案：验证文件完整性

1. 使用工具菜单 5 验证License文件
2. 如果无法解析，重新生成License文件
3. 确保文件传输过程中没有损坏
```

### 问题3：功能权限不足

```
# 客户需要更多功能权限
# 解决方案：生成新的License

1. 使用相同的客户信息和机器码
2. 更新授权功能列表
3. 生成新的License文件替换旧文件
```

## 最佳实践

1. **密钥管理**
   - 私钥文件加密保存
   - 定期备份密钥文件
   - 限制私钥访问权限

2. **License管理**
   - 记录每个客户的License信息
   - 建立License发放记录
   - 定期检查License到期情况

3. **客户支持**
   - 提供机器码获取指导
   - 建立License问题处理流程
   - 准备常见问题解答文档
