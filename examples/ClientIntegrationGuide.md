# License客户端集成指南

本指南介绍如何在您的项目中集成License验证功能。

## 1. 添加依赖

将License管理工具的JAR文件添加到您的项目依赖中：

### Maven项目
```xml
<dependency>
    <groupId>com.workplat</groupId>
    <artifactId>license-management-tool</artifactId>
    <version>1.0.0</version>
</dependency>
```

### Gradle项目
```gradle
implementation 'com.workplat:license-management-tool:1.0.0'
```

## 2. 基本使用方式

### 2.1 简单初始化
```java
import com.workplat.license.client.LicenseClient;
import com.workplat.license.validator.ValidationResult;

public class MyApplication {
    private LicenseClient licenseClient;
    
    public void initialize() {
        licenseClient = new LicenseClient();
        
        // 直接指定License文件路径和公钥
        String licenseFilePath = "./license.lic";
        String publicKey = "您的公钥内容";
        
        ValidationResult result = licenseClient.initialize(licenseFilePath, publicKey);
        
        if (result.isSuccess()) {
            System.out.println("License验证成功，应用程序可以正常运行");
        } else {
            System.err.println("License验证失败: " + result.getMessage());
            System.exit(1); // 阻止应用程序启动
        }
    }
}
```

### 2.2 从配置文件初始化
```java
public class MyApplication {
    private LicenseClient licenseClient;
    
    public void initialize() {
        licenseClient = new LicenseClient();
        
        // 从配置文件初始化
        ValidationResult result = licenseClient.initializeFromConfig("license-config.properties");
        
        if (!result.isSuccess()) {
            throw new RuntimeException("License验证失败: " + result.getMessage());
        }
    }
}
```

## 3. 功能权限检查

### 3.1 检查特定功能权限
```java
public class FeatureService {
    private LicenseClient licenseClient;
    
    public void generateReport() {
        try {
            if (licenseClient.hasFeature("REPORT_FEATURES")) {
                // 执行报表生成逻辑
                doGenerateReport();
            } else {
                throw new SecurityException("没有报表生成权限");
            }
        } catch (LicenseClient.LicenseException e) {
            throw new SecurityException("License验证失败", e);
        }
    }
    
    public void exportData() {
        try {
            if (licenseClient.hasFeature("EXPORT_FEATURES")) {
                // 执行数据导出逻辑
                doExportData();
            } else {
                throw new SecurityException("没有数据导出权限");
            }
        } catch (LicenseClient.LicenseException e) {
            throw new SecurityException("License验证失败", e);
        }
    }
}
```

### 3.2 用户数限制检查
```java
public class UserService {
    private LicenseClient licenseClient;
    
    public boolean allowUserLogin(int currentUserCount) {
        try {
            return licenseClient.checkUserLimit(currentUserCount + 1);
        } catch (LicenseClient.LicenseException e) {
            // License无效时拒绝登录
            return false;
        }
    }
    
    public int getMaxAllowedUsers() {
        try {
            return licenseClient.getMaxUsers();
        } catch (LicenseClient.LicenseException e) {
            return 0; // License无效时返回0
        }
    }
}
```

## 4. Spring Boot集成示例

### 4.1 配置类
```java
@Configuration
@EnableConfigurationProperties(LicenseProperties.class)
public class LicenseConfig {
    
    @Bean
    public LicenseClient licenseClient(LicenseProperties properties) {
        LicenseClient client = new LicenseClient();
        
        ValidationResult result = client.initialize(
            properties.getFilePath(), 
            properties.getPublicKey()
        );
        
        if (!result.isSuccess()) {
            throw new BeanCreationException("License验证失败: " + result.getMessage());
        }
        
        // 配置定时验证
        client.setValidationInterval(properties.getValidationInterval());
        client.setPeriodicValidationEnabled(properties.isPeriodicValidationEnabled());
        
        return client;
    }
}
```

### 4.2 配置属性类
```java
@ConfigurationProperties(prefix = "license")
@Data
public class LicenseProperties {
    private String filePath = "./license.lic";
    private String publicKey;
    private int validationInterval = 60;
    private boolean periodicValidationEnabled = true;
}
```

### 4.3 应用配置文件 (application.yml)
```yaml
license:
  file-path: ./license.lic
  public-key: |
    MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
  validation-interval: 30
  periodic-validation-enabled: true
```

### 4.4 权限检查注解
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireFeature {
    String value();
}

@Aspect
@Component
public class LicenseAspect {
    
    @Autowired
    private LicenseClient licenseClient;
    
    @Before("@annotation(requireFeature)")
    public void checkFeature(RequireFeature requireFeature) {
        try {
            if (!licenseClient.hasFeature(requireFeature.value())) {
                throw new SecurityException("没有功能权限: " + requireFeature.value());
            }
        } catch (LicenseClient.LicenseException e) {
            throw new SecurityException("License验证失败", e);
        }
    }
}
```

### 4.5 使用权限注解
```java
@RestController
public class ReportController {
    
    @RequireFeature("REPORT_FEATURES")
    @GetMapping("/reports/generate")
    public ResponseEntity<String> generateReport() {
        // 报表生成逻辑
        return ResponseEntity.ok("报表生成成功");
    }
    
    @RequireFeature("EXPORT_FEATURES")
    @GetMapping("/data/export")
    public ResponseEntity<String> exportData() {
        // 数据导出逻辑
        return ResponseEntity.ok("数据导出成功");
    }
}
```

## 5. 最佳实践

### 5.1 应用程序启动时验证
```java
@SpringBootApplication
public class MyApplication implements ApplicationRunner {
    
    @Autowired
    private LicenseClient licenseClient;
    
    public static void main(String[] args) {
        SpringApplication.run(MyApplication.class, args);
    }
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 应用启动后立即检查License状态
        if (!licenseClient.isLicenseValid()) {
            log.error("License无效，应用程序将退出");
            System.exit(1);
        }
        
        log.info("License验证通过，应用程序启动成功");
    }
}
```

### 5.2 优雅关闭
```java
@Component
public class LicenseShutdownHook {
    
    @Autowired
    private LicenseClient licenseClient;
    
    @PreDestroy
    public void shutdown() {
        licenseClient.shutdown();
    }
}
```

### 5.3 License状态监控
```java
@Component
public class LicenseMonitor {
    
    @Autowired
    private LicenseClient licenseClient;
    
    @Scheduled(fixedRate = 300000) // 每5分钟检查一次
    public void checkLicenseStatus() {
        try {
            if (!licenseClient.isLicenseValid()) {
                log.warn("License状态异常，请检查License文件");
                // 发送告警通知
                sendAlert("License状态异常");
            }
        } catch (Exception e) {
            log.error("License状态检查失败", e);
        }
    }
    
    private void sendAlert(String message) {
        // 实现告警通知逻辑
    }
}
```

## 6. 错误处理

### 6.1 常见错误及处理
```java
public class LicenseErrorHandler {
    
    public void handleLicenseError(LicenseClient.LicenseException e) {
        String message = e.getMessage();
        
        if (message.contains("过期")) {
            // License过期处理
            showLicenseExpiredDialog();
        } else if (message.contains("硬件绑定")) {
            // 硬件绑定失败处理
            showHardwareBindingErrorDialog();
        } else if (message.contains("未初始化")) {
            // 未初始化处理
            initializeLicense();
        } else {
            // 其他错误处理
            showGenericErrorDialog(message);
        }
    }
}
```

## 7. 安全注意事项

1. **公钥保护**: 将公钥嵌入到应用程序中，不要放在外部配置文件中
2. **License文件保护**: License文件应该有适当的文件权限保护
3. **错误信息**: 不要在错误信息中暴露敏感的License信息
4. **定时验证**: 启用定时验证以防止License被替换
5. **日志记录**: 记录License验证的关键事件，但不要记录敏感信息

## 8. 故障排除

### 8.1 常见问题
- **License文件未找到**: 检查文件路径是否正确
- **签名验证失败**: 确认公钥是否正确
- **硬件绑定失败**: 确认在正确的机器上运行
- **License过期**: 联系供应商续费或更新License

### 8.2 调试技巧
- 启用DEBUG日志级别查看详细信息
- 使用命令行工具验证License文件
- 检查系统时间是否正确
