# License管理工具

AI中央平台专业License管理工具，用于生成、管理和维护软件License文件。

## 功能特性

- 🔐 **RSA密钥对生成**：生成2048位RSA公私钥对
- 📄 **License文件生成**：支持多种License类型的生成
- 💻 **硬件绑定**：基于机器指纹的硬件绑定功能
- 🎛️ **功能权限控制**：细粒度的功能模块权限设置
- 📊 **License信息查看**：解析和查看License文件详细信息
- 🛠️ **命令行工具**：简单易用的交互式命令行界面

## 快速开始

### 1. 编译项目

```bash
mvn clean package
```

### 2. 运行工具

```bash
java -jar target/license-management-tool-1.0.0.jar
```

### 3. 使用流程

1. **生成密钥对**
   - 选择菜单选项 1
   - 设置密钥长度（推荐2048位）
   - 保存公私钥文件

2. **生成License文件**
   - 选择菜单选项 2
   - 输入产品信息和客户信息
   - 设置License类型和有效期
   - 选择是否绑定硬件
   - 输入私钥进行签名
   - 生成License文件

3. **查看机器码**
   - 选择菜单选项 4
   - 获取当前机器的硬件指纹

## License类型

- **TRIAL（试用版）**：适用于试用用户，功能有限
- **STANDARD（标准版）**：标准功能版本
- **ENTERPRISE（企业版）**：完整功能版本
- **DEVELOPMENT（开发版）**：开发测试使用

## 项目结构

```
src/main/java/com/workplat/license/
├── crypto/                 # 加密相关
│   ├── RSAKeyPairGenerator.java
│   └── RSASignatureUtil.java
├── generator/              # License生成
│   ├── LicenseGenerator.java
│   └── LicenseBuilder.java
├── model/                  # 数据模型
│   └── LicenseInfo.java
├── util/                   # 工具类
│   └── MachineCodeUtil.java
└── tool/                   # 命令行工具
    └── LicenseManagementMain.java
```

## 使用示例

### 生成试用License

```java
LicenseInfo licenseInfo = LicenseBuilder.createTrialLicense(
    "AI中央平台", 
    "测试客户", 
    30  // 30天试用期
).build();

String licenseFile = LicenseGenerator.generateLicenseFile(
    licenseInfo, 
    privateKey, 
    new File("./")
);
```

### 生成企业License

```java
LicenseInfo licenseInfo = LicenseBuilder.create()
    .product("AI中央平台", "1.0")
    .customer("企业客户", "ENT001")
    .type(LicenseInfo.LicenseType.ENTERPRISE)
    .validForDays(365)
    .bindCurrentMachine()
    .features("ALL_FEATURES", "ADMIN_FEATURES", "ENTERPRISE_FEATURES")
    .maxUsers(100)
    .build();
```

## 配置说明

### 密钥管理

- **公钥**：用于License验证，需要集成到主应用程序中
- **私钥**：用于License签名，需要妥善保管，不能泄露

### 硬件绑定

工具会自动获取以下硬件信息生成机器码：
- MAC地址
- CPU信息
- 操作系统信息
- 硬盘信息

### License文件格式

生成的License文件包含：
- 文件头部注释
- Base64编码的License数据
- RSA数字签名

## 安全注意事项

1. **私钥保护**：私钥文件必须妥善保管，建议使用密码保护
2. **签名验证**：所有License文件都包含数字签名，防止篡改
3. **硬件绑定**：可选的硬件绑定功能防止License被非法复制
4. **时间验证**：支持生效期和过期期的精确控制

## 故障排除

### 常见问题

1. **密钥格式错误**
   - 确保使用正确的Base64编码格式
   - 检查密钥是否完整

2. **机器码不匹配**
   - 确认目标机器的硬件配置
   - 重新获取目标机器的机器码

3. **License文件损坏**
   - 检查文件传输过程是否完整
   - 验证文件编码格式

## 版本历史

- **v1.0.0** - 初始版本
  - 基础License生成功能
  - RSA数字签名支持
  - 硬件绑定功能
  - 命令行工具界面

## 许可证

本项目为内部工具，仅供AI中央平台项目使用。

## 联系方式

如有问题或建议，请联系开发团队。
